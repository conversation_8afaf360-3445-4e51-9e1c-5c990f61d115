{"rustc": 16591470773350601817, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 2225463790103693989, "path": 12002835134438410230, "deps": [[996810380461694889, "sqlx_core", false, 12948767496353558132], [2713742371683562785, "syn", false, 1681830844553021405], [3060637413840920116, "proc_macro2", false, 7266838655266134858], [15733334431800349573, "sqlx_macros_core", false, 10020837536438775747], [17990358020177143287, "quote", false, 17335616060049607687]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-412e8c215509abdf\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}