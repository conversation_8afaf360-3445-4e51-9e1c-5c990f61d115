use tauri::State;
use crate::{database::Database, models::*, error::Result};
use chrono::{DateTime, Utc};

#[tauri::command]
pub async fn get_dashboard_stats(db: State<'_, Database>) -> Result<DashboardStats> {
    let total_projects: i32 = sqlx::query_scalar("SELECT COUNT(*) FROM projects")
        .fetch_one(&db.pool)
        .await?;

    let active_projects: i32 = sqlx::query_scalar(
        "SELECT COUNT(*) FROM projects WHERE status IN ('في الإنتاج', 'في انتظار الموافقة')"
    )
    .fetch_one(&db.pool)
    .await?;

    let total_employees: i32 = sqlx::query_scalar("SELECT COUNT(*) FROM employees")
        .fetch_one(&db.pool)
        .await?;

    let total_customers: i32 = sqlx::query_scalar("SELECT COUNT(*) FROM customers")
        .fetch_one(&db.pool)
        .await?;

    let monthly_revenue: f64 = sqlx::query_scalar(
        r#"
        SELECT COALESCE(SUM(amount), 0.0) FROM transactions 
        WHERE transaction_type = 'income' 
        AND strftime('%Y-%m', transaction_date) = strftime('%Y-%m', 'now')
        "#
    )
    .fetch_one(&db.pool)
    .await?;

    let monthly_expenses: f64 = sqlx::query_scalar(
        r#"
        SELECT COALESCE(SUM(amount), 0.0) FROM transactions 
        WHERE transaction_type = 'expense' 
        AND strftime('%Y-%m', transaction_date) = strftime('%Y-%m', 'now')
        "#
    )
    .fetch_one(&db.pool)
    .await?;

    let total_materials: i32 = sqlx::query_scalar("SELECT COUNT(*) FROM materials")
        .fetch_one(&db.pool)
        .await?;

    let low_stock_materials: i32 = sqlx::query_scalar(
        "SELECT COUNT(*) FROM materials WHERE current_stock <= min_stock"
    )
    .fetch_one(&db.pool)
    .await?;

    let pending_payments: f64 = sqlx::query_scalar(
        "SELECT COALESCE(SUM(amount), 0.0) FROM payments WHERE status = 'معلق'"
    )
    .fetch_one(&db.pool)
    .await?;

    let completed_projects_this_month: i32 = sqlx::query_scalar(
        r#"
        SELECT COUNT(*) FROM projects 
        WHERE status = 'مكتمل' 
        AND strftime('%Y-%m', actual_completion) = strftime('%Y-%m', 'now')
        "#
    )
    .fetch_one(&db.pool)
    .await?;

    Ok(DashboardStats {
        total_projects,
        active_projects,
        total_employees,
        total_customers,
        monthly_revenue,
        monthly_expenses,
        total_materials,
        low_stock_materials,
        pending_payments,
        completed_projects_this_month,
    })
}

#[tauri::command]
pub async fn get_recent_activities(db: State<'_, Database>) -> Result<Vec<RecentActivity>> {
    let activities = sqlx::query_as::<_, RecentActivity>(
        r#"
        SELECT 
            id,
            title,
            description,
            activity_type,
            created_at
        FROM activities 
        ORDER BY created_at DESC 
        LIMIT 10
        "#
    )
    .fetch_all(&db.pool)
    .await?;

    Ok(activities)
}
