#!/usr/bin/env python3
"""
Script to generate Tauri app icons from SVG
Requires: pip install Pillow cairosvg
"""

import os
from PIL import Image
import cairosvg

def svg_to_png(svg_path, png_path, size):
    """Convert SVG to PNG with specified size"""
    cairosvg.svg2png(
        url=svg_path,
        write_to=png_path,
        output_width=size,
        output_height=size
    )

def png_to_ico(png_path, ico_path):
    """Convert PNG to ICO"""
    img = Image.open(png_path)
    img.save(ico_path, format='ICO', sizes=[(32, 32), (64, 64), (128, 128), (256, 256)])

def png_to_icns(png_path, icns_path):
    """Convert PNG to ICNS (macOS)"""
    # This is a simplified version - for production use iconutil on macOS
    img = Image.open(png_path)
    img.save(icns_path, format='ICNS')

def main():
    svg_file = "src-tauri/icons/icon.svg"
    icons_dir = "src-tauri/icons"
    
    if not os.path.exists(svg_file):
        print(f"SVG file not found: {svg_file}")
        return
    
    # Generate PNG files
    sizes = [32, 128, 256, 512]
    for size in sizes:
        png_file = f"{icons_dir}/{size}x{size}.png"
        print(f"Generating {png_file}...")
        svg_to_png(svg_file, png_file, size)
        
        # Special case for 128x128@2x
        if size == 128:
            png_2x_file = f"{icons_dir}/<EMAIL>"
            svg_to_png(svg_file, png_2x_file, 256)
    
    # Generate ICO file for Windows
    ico_file = f"{icons_dir}/icon.ico"
    print(f"Generating {ico_file}...")
    png_to_ico(f"{icons_dir}/256x256.png", ico_file)
    
    # Generate ICNS file for macOS
    try:
        icns_file = f"{icons_dir}/icon.icns"
        print(f"Generating {icns_file}...")
        png_to_icns(f"{icons_dir}/512x512.png", icns_file)
    except Exception as e:
        print(f"Could not generate ICNS: {e}")
        print("For macOS icons, use iconutil on macOS system")
    
    # Copy main icon
    main_icon = f"{icons_dir}/icon.png"
    os.system(f'copy "{icons_dir}\\128x128.png" "{main_icon}"')
    
    print("Icon generation complete!")

if __name__ == "__main__":
    main()
