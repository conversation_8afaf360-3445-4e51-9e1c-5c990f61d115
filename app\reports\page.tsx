"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileText, Download, ArrowLeft, BarChart3, PieChart, TrendingUp, Calendar } from "lucide-react"
import Link from "next/link"

export default function ReportsManagement() {
  const [selectedReport, setSelectedReport] = useState("production")
  const [selectedPeriod, setSelectedPeriod] = useState("monthly")

  const reportTypes = [
    { value: "production", label: "تقرير الإنتاج", icon: BarChart3 },
    { value: "financial", label: "التقرير المالي", icon: TrendingUp },
    { value: "materials", label: "تقرير المواد", icon: Pie<PERSON>hart },
    { value: "payroll", label: "تقرير الرواتب", icon: Calendar },
  ]

  const productionData = [
    {
      project: "طقم طاولة طعام مخصص",
      client: "عائلة الأحمد",
      startDate: "2024-01-15",
      completionDate: "2024-01-25",
      status: "مكتمل",
      revenue: 3937.5, // Converted to LYD
      cost: 2800.0, // Converted to LYD
      profit: 1137.5, // Converted to LYD
      margin: "28.9%",
    },
    {
      project: "نظام خزائن مكتبية",
      client: "شركة التقنية المتقدمة",
      startDate: "2024-01-20",
      completionDate: "2024-02-05",
      status: "قيد التنفيذ",
      revenue: 2225.0, // Converted to LYD
      cost: 1575.0, // Converted to LYD
      profit: 650.0, // Converted to LYD
      margin: "29.2%",
    },
    {
      project: "طقم أثاث غرفة نوم",
      client: "منزل العائلة",
      startDate: "2024-01-10",
      completionDate: "2024-01-30",
      status: "مكتمل",
      revenue: 3100.0, // Converted to LYD
      cost: 2200.0, // Converted to LYD
      profit: 900.0, // Converted to LYD
      margin: "29.0%",
    },
  ]

  const financialSummary = {
    totalRevenue: 9262.5, // Converted to LYD
    totalCosts: 6575.0, // Converted to LYD
    totalProfit: 2687.5, // Converted to LYD
    profitMargin: "29.0%",
    payrollCosts: 7112.5, // Converted to LYD
    materialCosts: 3800.0, // Converted to LYD
    overheadCosts: 2125.0, // Converted to LYD
  }

  const materialUsage = [
    { material: "ألواح خشب البلوط", used: 45.5, unit: "م²", cost: 511.875 }, // Converted to LYD
    { material: "ألواح خشب الصنوبر", used: 32.0, unit: "م²", cost: 228.0 }, // Converted to LYD
    { material: "مسامير خشب", used: 2500, unit: "قطعة", cost: 93.75 }, // Converted to LYD
    { material: "غراء الخشب", used: 8, unit: "زجاجة", cost: 17.5 }, // Converted to LYD
    { material: "قشرة الماهوجني", used: 15.2, unit: "م²", cost: 247.0 }, // Converted to LYD
  ]

  const payrollSummary = [
    { department: "الإنتاج", employees: 25, totalPay: 4687.5, avgPay: 187.5 }, // Converted to LYD
    { department: "التصميم", employees: 8, totalPay: 1050.0, avgPay: 131.25 }, // Converted to LYD
    { department: "مراقبة الجودة", employees: 6, totalPay: 787.5, avgPay: 131.25 }, // Converted to LYD
    { department: "الإدارة", employees: 6, totalPay: 587.5, avgPay: 97.92 }, // Converted to LYD
  ]

  const generateReport = () => {
    alert(`جاري إنشاء ${reportTypes.find((r) => r.value === selectedReport)?.label} للفترة ${selectedPeriod}...`)
  }

  const exportReport = (format) => {
    alert(`جاري تصدير التقرير بصيغة ${format.toUpperCase()}...`)
  }

  const renderReportContent = () => {
    switch (selectedReport) {
      case "production":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">{productionData.length}</div>
                  <p className="text-sm text-gray-600">إجمالي المشاريع</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">
                    {financialSummary.totalRevenue.toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">
                    {financialSummary.totalProfit.toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">إجمالي الأرباح</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">{financialSummary.profitMargin}</div>
                  <p className="text-sm text-gray-600">متوسط الهامش</p>
                </CardContent>
              </Card>
            </div>

            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">مشاريع الإنتاج</CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">المشروع</TableHead>
                      <TableHead className="text-primary-900">العميل</TableHead>
                      <TableHead className="text-primary-900">تاريخ البدء</TableHead>
                      <TableHead className="text-primary-900">الإنجاز</TableHead>
                      <TableHead className="text-primary-900">الحالة</TableHead>
                      <TableHead className="text-primary-900">الإيرادات</TableHead>
                      <TableHead className="text-primary-900">الأرباح</TableHead>
                      <TableHead className="text-primary-900">الهامش</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {productionData.map((project, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium text-primary-900">{project.project}</TableCell>
                        <TableCell className="text-gray-700">{project.client}</TableCell>
                        <TableCell className="text-gray-700">{project.startDate}</TableCell>
                        <TableCell className="text-gray-700">{project.completionDate}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              project.status === "مكتمل"
                                ? "bg-secondary-100 text-secondary-800"
                                : "bg-orange-100 text-orange-800"
                            }
                          >
                            {project.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium">{project.revenue.toLocaleString()} د.ل</TableCell>
                        <TableCell className="text-secondary-600">{project.profit.toLocaleString()} د.ل</TableCell>
                        <TableCell className="font-medium">{project.margin}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )

      case "financial":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">
                    {financialSummary.totalRevenue.toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-red-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-red-900">
                    {financialSummary.totalCosts.toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">إجمالي التكاليف</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">
                    {financialSummary.totalProfit.toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">صافي الأرباح</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-2 border-primary-200">
                <CardHeader className="bg-primary-50">
                  <CardTitle className="text-primary-900">تفصيل التكاليف</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">تكاليف الرواتب:</span>
                      <span className="font-medium">{financialSummary.payrollCosts.toLocaleString()} د.ل</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">تكاليف المواد:</span>
                      <span className="font-medium">{financialSummary.materialCosts.toLocaleString()} د.ل</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">التكاليف العامة:</span>
                      <span className="font-medium">{financialSummary.overheadCosts.toLocaleString()} د.ل</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between items-center font-bold">
                      <span>إجمالي التكاليف:</span>
                      <span>
                        {(
                          financialSummary.payrollCosts +
                          financialSummary.materialCosts +
                          financialSummary.overheadCosts
                        ).toLocaleString()}{" "}
                        د.ل
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 border-secondary-200">
                <CardHeader className="bg-secondary-50">
                  <CardTitle className="text-secondary-900">تحليل الربحية</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">إجمالي الإيرادات:</span>
                      <span className="font-medium">{financialSummary.totalRevenue.toLocaleString()} د.ل</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">إجمالي المصروفات:</span>
                      <span className="font-medium text-red-600">
                        -
                        {(
                          financialSummary.payrollCosts +
                          financialSummary.materialCosts +
                          financialSummary.overheadCosts
                        ).toLocaleString()}{" "}
                        د.ل
                      </span>
                    </div>
                    <div className="border-t pt-2 flex justify-between items-center font-bold text-secondary-600">
                      <span>صافي الأرباح:</span>
                      <span>
                        {(
                          financialSummary.totalRevenue -
                          financialSummary.payrollCosts -
                          financialSummary.materialCosts -
                          financialSummary.overheadCosts
                        ).toLocaleString()}{" "}
                        د.ل
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">هامش الربح:</span>
                      <span className="font-medium">
                        {(
                          ((financialSummary.totalRevenue -
                            financialSummary.payrollCosts -
                            financialSummary.materialCosts -
                            financialSummary.overheadCosts) /
                            financialSummary.totalRevenue) *
                          100
                        ).toFixed(1)}
                        %
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )

      case "materials":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">{materialUsage.length}</div>
                  <p className="text-sm text-gray-600">المواد المستخدمة</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">
                    {materialUsage.reduce((sum, m) => sum + m.cost, 0).toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">إجمالي التكلفة</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">
                    {materialUsage
                      .filter((m) => m.unit === "م²")
                      .reduce((sum, m) => sum + m.used, 0)
                      .toFixed(1)}{" "}
                    م²
                  </div>
                  <p className="text-sm text-gray-600">الخشب المستخدم</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">
                    {(
                      materialUsage.reduce((sum, m) => sum + m.cost, 0) /
                      materialUsage.reduce((sum, m) => (m.unit === "م²" ? sum + m.used : sum), 0)
                    ).toFixed(2)}{" "}
                    د.ل
                  </div>
                  <p className="text-sm text-gray-600">متوسط التكلفة/م²</p>
                </CardContent>
              </Card>
            </div>

            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">تفاصيل استخدام المواد</CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">المادة</TableHead>
                      <TableHead className="text-primary-900">الكمية المستخدمة</TableHead>
                      <TableHead className="text-primary-900">الوحدة</TableHead>
                      <TableHead className="text-primary-900">إجمالي التكلفة</TableHead>
                      <TableHead className="text-primary-900">التكلفة لكل وحدة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {materialUsage.map((material, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium text-primary-900">{material.material}</TableCell>
                        <TableCell className="text-gray-700">{material.used}</TableCell>
                        <TableCell className="text-gray-700">{material.unit}</TableCell>
                        <TableCell className="font-medium">{material.cost.toFixed(2)} د.ل</TableCell>
                        <TableCell className="text-gray-700">
                          {(material.cost / material.used).toFixed(2)} د.ل
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )

      case "payroll":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">
                    {payrollSummary.reduce((sum, d) => sum + d.employees, 0)}
                  </div>
                  <p className="text-sm text-gray-600">إجمالي الموظفين</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">
                    {payrollSummary.reduce((sum, d) => sum + d.totalPay, 0).toLocaleString()} د.ل
                  </div>
                  <p className="text-sm text-gray-600">إجمالي الرواتب</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-primary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary-900">
                    {(
                      payrollSummary.reduce((sum, d) => sum + d.totalPay, 0) /
                      payrollSummary.reduce((sum, d) => sum + d.employees, 0)
                    ).toFixed(2)}{" "}
                    د.ل
                  </div>
                  <p className="text-sm text-gray-600">متوسط لكل موظف</p>
                </CardContent>
              </Card>
              <Card className="border-2 border-secondary-200">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-secondary-900">{payrollSummary.length}</div>
                  <p className="text-sm text-gray-600">الأقسام</p>
                </CardContent>
              </Card>
            </div>

            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">الرواتب حسب القسم</CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">القسم</TableHead>
                      <TableHead className="text-primary-900">الموظفين</TableHead>
                      <TableHead className="text-primary-900">إجمالي الراتب</TableHead>
                      <TableHead className="text-primary-900">متوسط الراتب</TableHead>
                      <TableHead className="text-primary-900">% من الإجمالي</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {payrollSummary.map((dept, index) => {
                      const totalPayroll = payrollSummary.reduce((sum, d) => sum + d.totalPay, 0)
                      const percentage = ((dept.totalPay / totalPayroll) * 100).toFixed(1)

                      return (
                        <TableRow key={index}>
                          <TableCell className="font-medium text-primary-900">{dept.department}</TableCell>
                          <TableCell className="text-gray-700">{dept.employees}</TableCell>
                          <TableCell className="font-medium">{dept.totalPay.toLocaleString()} د.ل</TableCell>
                          <TableCell className="text-gray-700">{dept.avgPay.toFixed(2)} د.ل</TableCell>
                          <TableCell className="text-secondary-600">{percentage}%</TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )

      default:
        return <div className="text-center text-gray-500 py-8">اختر نوع التقرير لعرض التفاصيل</div>
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/">
              <Button variant="outline" size="sm" className="border-primary-300 text-primary-700 hover:bg-primary-50">
                <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
                العودة للوحة التحكم
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-primary-900">التقارير والتحليلات</h1>
              <p className="text-gray-600">إنشاء تقارير تجارية شاملة بالدينار الليبي</p>
            </div>
          </div>

          <div className="flex space-x-2 space-x-reverse">
            <Button
              variant="outline"
              onClick={() => exportReport("pdf")}
              className="border-primary-300 text-primary-700 hover:bg-primary-50"
            >
              <Download className="h-4 w-4 ml-2" />
              تصدير PDF
            </Button>
            <Button
              variant="outline"
              onClick={() => exportReport("excel")}
              className="border-secondary-300 text-secondary-700 hover:bg-secondary-50"
            >
              <Download className="h-4 w-4 ml-2" />
              تصدير Excel
            </Button>
          </div>
        </div>

        {/* Report Controls */}
        <Card className="mb-8 border-2 border-primary-200">
          <CardHeader className="bg-primary-50">
            <CardTitle className="flex items-center space-x-2 space-x-reverse text-primary-900">
              <FileText className="h-5 w-5" />
              <span>مولد التقارير</span>
            </CardTitle>
            <CardDescription className="text-gray-600">اختر نوع التقرير والفترة الزمنية</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <div>
                <label className="text-sm font-medium mb-2 block text-primary-900">نوع التقرير</label>
                <Select value={selectedReport} onValueChange={setSelectedReport}>
                  <SelectTrigger className="border-primary-200 focus:border-primary-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <type.icon className="h-4 w-4" />
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block text-primary-900">الفترة الزمنية</label>
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="border-primary-200 focus:border-primary-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekly">أسبوعي</SelectItem>
                    <SelectItem value="monthly">شهري</SelectItem>
                    <SelectItem value="quarterly">ربع سنوي</SelectItem>
                    <SelectItem value="yearly">سنوي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={generateReport} className="bg-secondary-600 hover:bg-secondary-700">
                <BarChart3 className="h-4 w-4 ml-2" />
                إنشاء التقرير
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Report Content */}
        <Card className="border-2 border-primary-200">
          <CardHeader className="bg-primary-50">
            <CardTitle className="text-primary-900">
              {reportTypes.find((r) => r.value === selectedReport)?.label} -{" "}
              {selectedPeriod === "weekly"
                ? "أسبوعي"
                : selectedPeriod === "monthly"
                  ? "شهري"
                  : selectedPeriod === "quarterly"
                    ? "ربع سنوي"
                    : "سنوي"}
            </CardTitle>
            <CardDescription className="text-gray-600">
              تم الإنشاء في {new Date().toLocaleDateString("ar-SA")} - جميع المبالغ بالدينار الليبي
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">{renderReportContent()}</CardContent>
        </Card>
      </div>
    </div>
  )
}
