"use client"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Phone, Mail, MapPin, Calendar, FileText, DollarSign, User, Package } from "lucide-react"
import Link from "next/link"

export default function CustomerDetail({ params }: { params: { id: string } }) {
  const customerId = Number.parseInt(params.id)

  // Mock customer data - in real app, fetch from API
  const customer = {
    id: customerId,
    name: "عائلة الأحمد",
    email: "<EMAIL>",
    phone: "+218 91 234 5678",
    address: "شارع الجمهورية، طرابلس",
    registrationDate: "2024-01-10",
    totalProjects: 2,
    totalSpent: 6162.5,
    status: "نشط",
    lastInteraction: "2024-01-25",
    customerType: "عائلي",
  }

  const projects = [
    {
      id: 1,
      name: "طقم طاولة طعام مخصص",
      status: "مكتمل",
      startDate: "2024-01-15",
      completionDate: "2024-01-25",
      totalAmount: 3937.5,
      assignedEmployee: "أحمد محمد السالم",
      stage: "مكتمل",
    },
    {
      id: 2,
      name: "خزانة غرفة النوم",
      status: "في الإنتاج",
      startDate: "2024-02-01",
      completionDate: "2024-02-20",
      totalAmount: 2225.0,
      assignedEmployee: "مريم أحمد الغامدي",
      stage: "تصنيع",
    },
  ]

  const invoices = [
    {
      id: 1,
      projectId: 1,
      type: "أولية",
      amount: 2756.25,
      date: "2024-01-15",
      status: "مدفوعة",
      invoiceNumber: "INV-001-01",
    },
    {
      id: 2,
      projectId: 1,
      type: "تصنيع",
      amount: 2756.25,
      date: "2024-01-16",
      status: "مدفوعة",
      invoiceNumber: "INV-001-02",
    },
    {
      id: 3,
      projectId: 1,
      type: "نهائية",
      amount: 1181.25,
      date: "2024-01-25",
      status: "مدفوعة",
      invoiceNumber: "INV-001-03",
    },
    {
      id: 4,
      projectId: 2,
      type: "أولية",
      amount: 1557.5,
      date: "2024-02-01",
      status: "مدفوعة",
      invoiceNumber: "INV-002-01",
    },
    {
      id: 5,
      projectId: 2,
      type: "تصنيع",
      amount: 1557.5,
      date: "2024-02-02",
      status: "مدفوعة",
      invoiceNumber: "INV-002-02",
    },
  ]

  const interactions = [
    {
      id: 1,
      date: "2024-01-25",
      type: "تسليم مشروع",
      description: "تم تسليم طقم طاولة الطعام المخصص بنجاح",
      employee: "أحمد محمد السالم",
    },
    {
      id: 2,
      date: "2024-02-02",
      type: "بدء التصنيع",
      description: "بدء تصنيع خزانة غرفة النوم",
      employee: "مريم أحمد الغامدي",
    },
    {
      id: 3,
      date: "2024-02-01",
      type: "دفع فاتورة",
      description: "تم دفع الفاتورة الأولية لخزانة غرفة النوم",
      employee: "قسم المحاسبة",
    },
    {
      id: 4,
      date: "2024-01-16",
      type: "انتقال للتصنيع",
      description: "انتقال مشروع طقم الطاولة من المرحلة الأولية إلى التصنيع",
      employee: "قسم الإنتاج",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "مكتمل":
        return "bg-secondary-100 text-secondary-800"
      case "في الإنتاج":
        return "bg-primary-100 text-primary-800"
      case "في انتظار":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getInvoiceStatusColor = (status) => {
    switch (status) {
      case "مدفوعة":
        return "bg-secondary-100 text-secondary-800"
      case "معلقة":
        return "bg-orange-100 text-orange-800"
      case "متأخرة":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getInvoiceTypeColor = (type) => {
    switch (type) {
      case "أولية":
        return "bg-blue-100 text-blue-800"
      case "تصنيع":
        return "bg-primary-100 text-primary-800"
      case "نهائية":
        return "bg-secondary-100 text-secondary-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center space-x-4 space-x-reverse">
          <Link href="/crm">
            <Button variant="outline" size="sm" className="border-primary-300 text-primary-700 hover:bg-primary-50">
              <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
              العودة لقائمة العملاء
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-primary-900">{customer.name}</h1>
            <p className="text-gray-600">تفاصيل العميل وسجل التفاعلات</p>
          </div>
        </div>

        {/* Customer Info Card */}
        <Card className="mb-8 border-2 border-primary-200">
          <CardHeader className="bg-primary-50">
            <CardTitle className="flex items-center space-x-2 space-x-reverse text-primary-900">
              <User className="h-5 w-5" />
              <span>معلومات العميل</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-primary-900 mb-2">معلومات الاتصال</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{customer.phone}</span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{customer.email}</span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{customer.address}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-primary-900 mb-2">تفاصيل الحساب</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">نوع العميل:</span>
                      <Badge className="bg-secondary-100 text-secondary-800">{customer.customerType}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">تاريخ التسجيل:</span>
                      <span className="text-sm">{customer.registrationDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">آخر تفاعل:</span>
                      <span className="text-sm">{customer.lastInteraction}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-primary-900 mb-2">إحصائيات المشاريع</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">إجمالي المشاريع:</span>
                      <span className="text-sm font-medium">{customer.totalProjects}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">المشاريع المكتملة:</span>
                      <span className="text-sm font-medium">{projects.filter((p) => p.status === "مكتمل").length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">المشاريع الجارية:</span>
                      <span className="text-sm font-medium">
                        {projects.filter((p) => p.status === "في الإنتاج").length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-primary-900 mb-2">المعلومات المالية</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">إجمالي الإنفاق:</span>
                      <span className="text-sm font-bold text-secondary-600">
                        {customer.totalSpent.toLocaleString()} د.ل
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">متوسط قيمة المشروع:</span>
                      <span className="text-sm font-medium">
                        {(customer.totalSpent / customer.totalProjects).toFixed(2)} د.ل
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">الحالة:</span>
                      <Badge className="bg-secondary-100 text-secondary-800">{customer.status}</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for different sections */}
        <Tabs defaultValue="projects" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="projects" className="flex items-center space-x-2 space-x-reverse">
              <Package className="h-4 w-4" />
              <span>المشاريع</span>
            </TabsTrigger>
            <TabsTrigger value="invoices" className="flex items-center space-x-2 space-x-reverse">
              <FileText className="h-4 w-4" />
              <span>الفواتير</span>
            </TabsTrigger>
            <TabsTrigger value="payments" className="flex items-center space-x-2 space-x-reverse">
              <DollarSign className="h-4 w-4" />
              <span>المدفوعات</span>
            </TabsTrigger>
            <TabsTrigger value="interactions" className="flex items-center space-x-2 space-x-reverse">
              <Calendar className="h-4 w-4" />
              <span>سجل التفاعلات</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="projects">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">مشاريع العميل</CardTitle>
                <CardDescription className="text-gray-600">جميع المشاريع المرتبطة بهذا العميل</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">اسم المشروع</TableHead>
                      <TableHead className="text-primary-900">الموظف المكلف</TableHead>
                      <TableHead className="text-primary-900">تاريخ البدء</TableHead>
                      <TableHead className="text-primary-900">تاريخ الإنجاز</TableHead>
                      <TableHead className="text-primary-900">المرحلة</TableHead>
                      <TableHead className="text-primary-900">الحالة</TableHead>
                      <TableHead className="text-primary-900">القيمة الإجمالية</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {projects.map((project) => (
                      <TableRow key={project.id}>
                        <TableCell className="font-medium text-primary-900">{project.name}</TableCell>
                        <TableCell className="text-gray-700">{project.assignedEmployee}</TableCell>
                        <TableCell className="text-gray-700">{project.startDate}</TableCell>
                        <TableCell className="text-gray-700">{project.completionDate}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(project.stage)}>{project.stage}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                        </TableCell>
                        <TableCell className="font-medium text-secondary-600">
                          {project.totalAmount.toLocaleString()} د.ل
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">فواتير العميل</CardTitle>
                <CardDescription className="text-gray-600">
                  جميع الفواتير (أولية، تصنيع، نهائية) لكل مشروع
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">رقم الفاتورة</TableHead>
                      <TableHead className="text-primary-900">المشروع</TableHead>
                      <TableHead className="text-primary-900">نوع الفاتورة</TableHead>
                      <TableHead className="text-primary-900">المبلغ</TableHead>
                      <TableHead className="text-primary-900">التاريخ</TableHead>
                      <TableHead className="text-primary-900">الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoices.map((invoice) => {
                      const project = projects.find((p) => p.id === invoice.projectId)
                      return (
                        <TableRow key={invoice.id}>
                          <TableCell className="font-medium text-primary-900">{invoice.invoiceNumber}</TableCell>
                          <TableCell className="text-gray-700">{project?.name}</TableCell>
                          <TableCell>
                            <Badge className={getInvoiceTypeColor(invoice.type)}>{invoice.type}</Badge>
                          </TableCell>
                          <TableCell className="font-medium">{invoice.amount.toLocaleString()} د.ل</TableCell>
                          <TableCell className="text-gray-700">{invoice.date}</TableCell>
                          <TableCell>
                            <Badge className={getInvoiceStatusColor(invoice.status)}>{invoice.status}</Badge>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payments">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">سجل المدفوعات</CardTitle>
                <CardDescription className="text-gray-600">تفاصيل جميع المدفوعات المستلمة من العميل</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="bg-secondary-50 p-4 rounded-lg border-2 border-secondary-200">
                    <h4 className="font-medium text-secondary-900 mb-2">إجمالي المدفوعات</h4>
                    <p className="text-2xl font-bold text-secondary-600">{customer.totalSpent.toLocaleString()} د.ل</p>
                  </div>
                  <div className="bg-primary-50 p-4 rounded-lg border-2 border-primary-200">
                    <h4 className="font-medium text-primary-900 mb-2">المدفوعات المكتملة</h4>
                    <p className="text-2xl font-bold text-primary-600">
                      {invoices.filter((i) => i.status === "مدفوعة").length}
                    </p>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg border-2 border-orange-200">
                    <h4 className="font-medium text-orange-900 mb-2">المدفوعات المعلقة</h4>
                    <p className="text-2xl font-bold text-orange-600">
                      {invoices.filter((i) => i.status === "معلقة").length}
                    </p>
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">رقم الفاتورة</TableHead>
                      <TableHead className="text-primary-900">نوع الدفعة</TableHead>
                      <TableHead className="text-primary-900">المبلغ</TableHead>
                      <TableHead className="text-primary-900">تاريخ الدفع</TableHead>
                      <TableHead className="text-primary-900">الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoices
                      .filter((invoice) => invoice.status === "مدفوعة")
                      .map((invoice) => (
                        <TableRow key={invoice.id}>
                          <TableCell className="font-medium text-primary-900">{invoice.invoiceNumber}</TableCell>
                          <TableCell>
                            <Badge className={getInvoiceTypeColor(invoice.type)}>{invoice.type}</Badge>
                          </TableCell>
                          <TableCell className="font-medium text-secondary-600">
                            {invoice.amount.toLocaleString()} د.ل
                          </TableCell>
                          <TableCell className="text-gray-700">{invoice.date}</TableCell>
                          <TableCell>
                            <Badge className="bg-secondary-100 text-secondary-800">مدفوعة</Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="interactions">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">سجل التفاعلات</CardTitle>
                <CardDescription className="text-gray-600">تاريخ شامل لجميع التفاعلات مع العميل</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {interactions.map((interaction) => (
                    <div
                      key={interaction.id}
                      className="flex items-start space-x-4 space-x-reverse p-4 border-2 border-gray-100 rounded-lg"
                    >
                      <div className="w-3 h-3 bg-primary-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-primary-900">{interaction.type}</h4>
                          <span className="text-sm text-gray-500">{interaction.date}</span>
                        </div>
                        <p className="text-gray-700 mb-2">{interaction.description}</p>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <User className="h-3 w-3 text-gray-500" />
                          <span className="text-xs text-gray-500">{interaction.employee}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
