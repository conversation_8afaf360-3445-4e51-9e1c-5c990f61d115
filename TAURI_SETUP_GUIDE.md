# دليل إعداد تطبيق Tauri - نظام إدارة مصنع الأثاث

## نظرة عامة
تم تحويل تطبيق الويب بنجاح إلى تطبيق سطح مكتب أصلي باستخدام Tauri. التطبيق يعمل بشكل كامل في وضع عدم الاتصال مع قاعدة بيانات SQLite محلية.

## المتطلبات المسبقة

### 1. Node.js و npm/pnpm
```bash
# تحقق من وجود Node.js
node --version
npm --version
```

### 2. Rust
```bash
# تثبيت Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
# أو على Windows
# قم بتحميل rustup-init.exe من https://rustup.rs/

# تحقق من التثبيت
rustc --version
cargo --version
```

### 3. متطلبات النظام الإضافية

#### Windows
```bash
# تثبيت Microsoft C++ Build Tools
# قم بتحميل Visual Studio Installer وتثبيت "C++ build tools"
```

#### macOS
```bash
# تثبيت Xcode Command Line Tools
xcode-select --install
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install libwebkit2gtk-4.0-dev \
    build-essential \
    curl \
    wget \
    libssl-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev
```

## إعداد المشروع

### الطريقة السريعة (مستحسنة)
```bash
# على Windows
fix-dependencies.bat

# على Linux/macOS
./fix-dependencies.sh
```

### الطريقة اليدوية

#### 1. حل تعارض التبعيات
```bash
# حذف الملفات القديمة
rm -rf node_modules package-lock.json out

# تثبيت التبعيات مع تجاهل التعارضات
npm install --legacy-peer-deps
```

#### 2. تثبيت Tauri CLI (اختياري)
```bash
# تثبيت Tauri CLI عالمياً
npm install -g @tauri-apps/cli
# أو
cargo install tauri-cli
```

#### 3. التحقق من الإعداد
```bash
# التحقق من إعداد Tauri
tauri info
```

## تشغيل التطبيق

### وضع التطوير
```bash
# تشغيل التطبيق في وضع التطوير
npm run tauri:dev
# أو
tauri dev
```

### بناء التطبيق للإنتاج
```bash
# بناء التطبيق
npm run tauri:build
# أو
tauri build
```

## الميزات المُحققة

### ✅ العمل في وضع عدم الاتصال
- قاعدة بيانات SQLite محلية
- جميع البيانات محفوظة محلياً
- لا يوجد اعتماد على الإنترنت

### ✅ الوظائف الأساسية
- إدارة المواد والمخزون
- إدارة العملاء (CRM)
- إدارة الموظفين والرواتب
- إدارة المشاريع والإنتاج
- إدارة المدفوعات والفواتير
- إدارة الخزينة والمعاملات المالية
- التقارير والإحصائيات
- النسخ الاحتياطي والاستعادة

### ✅ واجهة المستخدم
- واجهة عربية كاملة (RTL)
- تصميم حديث ومتجاوب
- مؤشر حالة الاتصال
- إشعارات النظام

### ✅ الأمان والأداء
- تشفير قاعدة البيانات
- نسخ احتياطية آمنة
- أداء سريع مع Rust backend
- استهلاك ذاكرة منخفض

## بنية المشروع

```
├── app/                    # صفحات Next.js
├── components/             # مكونات React
├── lib/                   # مكتبات ومساعدات
├── hooks/                 # React hooks
├── src-tauri/            # كود Rust
│   ├── src/              # كود المصدر
│   ├── icons/            # أيقونات التطبيق
│   ├── migrations/       # ملفات قاعدة البيانات
│   └── tauri.conf.json   # تكوين Tauri
├── public/               # ملفات ثابتة
└── out/                  # ملفات البناء
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في بناء Rust
```bash
# تحديث Rust
rustup update

# تنظيف cache
cargo clean
```

#### 2. مشاكل في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
# المسار: %APPDATA%/furniture_factory/furniture_factory.db
```

#### 3. مشاكل في الأيقونات
```bash
# إعادة إنشاء الأيقونات
python create_simple_icons.py
```

## التوزيع

### Windows
- ملف `.msi` في `src-tauri/target/release/bundle/msi/`
- ملف `.exe` في `src-tauri/target/release/`

### macOS
- ملف `.dmg` في `src-tauri/target/release/bundle/dmg/`
- ملف `.app` في `src-tauri/target/release/bundle/macos/`

### Linux
- ملف `.deb` في `src-tauri/target/release/bundle/deb/`
- ملف `.AppImage` في `src-tauri/target/release/bundle/appimage/`

## الدعم والصيانة

### تحديث التطبيق
- يمكن إضافة نظام تحديث تلقائي باستخدام Tauri updater
- النسخ الاحتياطية تتم تلقائياً قبل التحديثات

### مراقبة الأداء
- استخدم أدوات Tauri المدمجة لمراقبة الأداء
- تحقق من logs في وضع التطوير

## الخطوات التالية المقترحة

1. **اختبار شامل**: اختبر جميع الوظائف في بيئات مختلفة
2. **تحسين الأداء**: راجع استعلامات قاعدة البيانات
3. **إضافة ميزات**: نظام تحديث تلقائي، تصدير متقدم
4. **الأمان**: تشفير إضافي للبيانات الحساسة
5. **التوثيق**: دليل مستخدم مفصل

## معلومات الاتصال والدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
**تم إنشاء هذا الدليل بواسطة Augment Agent**
**تاريخ الإنشاء: {{ current_date }}**
