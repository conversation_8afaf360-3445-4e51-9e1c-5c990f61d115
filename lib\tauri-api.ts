import { invoke } from "@tauri-apps/api/tauri"
import { save, open } from "@tauri-apps/api/dialog"
import { writeTextFile, readTextFile } from "@tauri-apps/api/fs"
import { notification } from "@tauri-apps/api"

// Types
export interface Material {
  id: number
  name: string
  category: string
  current_stock: number
  min_stock: number
  max_stock: number
  unit: string
  cost_per_unit: number
  supplier: string
  last_restocked: string
  created_at: string
  updated_at: string
}

export interface CreateMaterial {
  name: string
  category: string
  current_stock: number
  min_stock: number
  max_stock: number
  unit: string
  cost_per_unit: number
  supplier: string
}

export interface Customer {
  id: number
  name: string
  email?: string
  phone?: string
  address?: string
  customer_type: string
  status: string
  total_projects: number
  total_spent: number
  discount: number
  discount_reason?: string
  registration_date: string
  last_interaction: string
  created_at: string
  updated_at: string
}

export interface CreateCustomer {
  name: string
  email?: string
  phone?: string
  address?: string
  customer_type: string
}

export interface DashboardStats {
  total_projects: number
  active_projects: number
  total_employees: number
  total_customers: number
  monthly_revenue: number
  monthly_expenses: number
  total_materials: number
  low_stock_materials: number
  pending_payments: number
  completed_projects_this_month: number
}

export interface RecentActivity {
  id: number
  title: string
  description: string
  activity_type: string
  created_at: string
}

// Materials API
export const materialsApi = {
  getAll: (): Promise<Material[]> => invoke("get_materials"),
  create: (material: CreateMaterial): Promise<Material> => invoke("create_material", { material }),
  update: (id: number, material: CreateMaterial): Promise<Material> => invoke("update_material", { id, material }),
  delete: (id: number): Promise<boolean> => invoke("delete_material", { id }),
  import: (materials: CreateMaterial[]): Promise<Material[]> => invoke("import_materials", { materials }),
  export: (): Promise<string> => invoke("export_materials"),
}

// Customers API
export const customersApi = {
  getAll: (): Promise<Customer[]> => invoke("get_customers"),
  getById: (id: number): Promise<Customer | null> => invoke("get_customer_by_id", { id }),
  create: (customer: CreateCustomer): Promise<Customer> => invoke("create_customer", { customer }),
  update: (id: number, customer: CreateCustomer): Promise<Customer> => invoke("update_customer", { id, customer }),
  delete: (id: number): Promise<boolean> => invoke("delete_customer", { id }),
}

// Dashboard API
export const dashboardApi = {
  getStats: (): Promise<DashboardStats> => invoke("get_dashboard_stats"),
  getRecentActivities: (): Promise<RecentActivity[]> => invoke("get_recent_activities"),
}

// File operations
export const fileApi = {
  saveFile: async (content: string, defaultName: string, filters?: any[]) => {
    const filePath = await save({
      defaultPath: defaultName,
      filters: filters || [
        { name: "CSV Files", extensions: ["csv"] },
        { name: "All Files", extensions: ["*"] },
      ],
    })

    if (filePath) {
      await writeTextFile(filePath, content)
      await notification.sendNotification({
        title: "تم الحفظ بنجاح",
        body: `تم حفظ الملف في: ${filePath}`,
      })
    }

    return filePath
  },

  openFile: async (filters?: any[]) => {
    const filePath = await open({
      multiple: false,
      filters: filters || [
        { name: "CSV Files", extensions: ["csv"] },
        { name: "Excel Files", extensions: ["xlsx", "xls"] },
        { name: "All Files", extensions: ["*"] },
      ],
    })

    if (filePath && typeof filePath === "string") {
      const content = await readTextFile(filePath)
      return { path: filePath, content }
    }

    return null
  },
}

// Notifications
export const notificationApi = {
  success: (title: string, body: string) => {
    notification.sendNotification({
      title,
      body,
      icon: "success",
    })
  },

  error: (title: string, body: string) => {
    notification.sendNotification({
      title,
      body,
      icon: "error",
    })
  },

  info: (title: string, body: string) => {
    notification.sendNotification({
      title,
      body,
      icon: "info",
    })
  },
}

// Error handling wrapper
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,\
  errorMessage: string = 'حدث خطأ غير متوقع'
)
: Promise<T | null> =>
{
  try {
    return await operation()
  } catch (error) {
    console.error("API Error:", error)
    await notificationApi.error("خطأ", `${errorMessage}: ${error}`)
    return null
  }
}
