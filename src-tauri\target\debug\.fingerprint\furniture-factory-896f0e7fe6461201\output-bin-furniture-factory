{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":5428,"byte_end":6023,"line_start":134,"line_end":148,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let result = sqlx::query!(","highlight_start":22,"highlight_end":35},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            INSERT INTO projects (name, customer_id, assigned_employee_id, furniture_type, square_meters, designer_fee, factory_overhead, profit_margin, expected_completion)","highlight_start":1,"highlight_end":174},{"text":"            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)","highlight_start":1,"highlight_end":47},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            project.name,","highlight_start":1,"highlight_end":26},{"text":"            project.customer_id,","highlight_start":1,"highlight_end":33},{"text":"            project.assigned_employee_id,","highlight_start":1,"highlight_end":42},{"text":"            project.furniture_type,","highlight_start":1,"highlight_end":36},{"text":"            project.square_meters,","highlight_start":1,"highlight_end":35},{"text":"            project.designer_fee,","highlight_start":1,"highlight_end":34},{"text":"            project.factory_overhead,","highlight_start":1,"highlight_end":38},{"text":"            project.profit_margin,","highlight_start":1,"highlight_end":35},{"text":"            project.expected_completion","highlight_start":1,"highlight_end":40},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:134:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ____________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       INSERT INTO projects (name, customer_id, assigned_employee_id, furniture_type, square_meters, designer_fee, factory_overhead, p\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       project.expected_completion\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_______^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":6730,"byte_end":7396,"line_start":163,"line_end":179,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        sqlx::query!(","highlight_start":9,"highlight_end":22},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            UPDATE projects","highlight_start":1,"highlight_end":28},{"text":"            SET name = ?, customer_id = ?, assigned_employee_id = ?, furniture_type = ?, square_meters = ?, designer_fee = ?, factory_overhead = ?, profit_margin = ?, expected_completion = ?, updated_at = CURRENT_TIMESTAMP","highlight_start":1,"highlight_end":223},{"text":"            WHERE id = ?","highlight_start":1,"highlight_end":25},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            project.name,","highlight_start":1,"highlight_end":26},{"text":"            project.customer_id,","highlight_start":1,"highlight_end":33},{"text":"            project.assigned_employee_id,","highlight_start":1,"highlight_end":42},{"text":"            project.furniture_type,","highlight_start":1,"highlight_end":36},{"text":"            project.square_meters,","highlight_start":1,"highlight_end":35},{"text":"            project.designer_fee,","highlight_start":1,"highlight_end":34},{"text":"            project.factory_overhead,","highlight_start":1,"highlight_end":38},{"text":"            project.profit_margin,","highlight_start":1,"highlight_end":35},{"text":"            project.expected_completion,","highlight_start":1,"highlight_end":41},{"text":"            id","highlight_start":1,"highlight_end":15},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:163:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m164\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       UPDATE projects\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       SET name = ?, customer_id = ?, assigned_employee_id = ?, furniture_type = ?, square_meters = ?, designer_fee = ?, factory_overh\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_______^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":8035,"byte_end":8088,"line_start":194,"line_end":194,"column_start":22,"column_end":75,"is_primary":false,"text":[{"text":"        let result = sqlx::query!(\"DELETE FROM projects WHERE id = ?\", id)","highlight_start":22,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:194:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let result = sqlx::query!(\"DELETE FROM projects WHERE id = ?\", id)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":9487,"byte_end":9818,"line_start":225,"line_end":235,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let result = sqlx::query!(","highlight_start":22,"highlight_end":35},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            INSERT INTO payments (project_id, payment_type, amount, status, invoice_number)","highlight_start":1,"highlight_end":92},{"text":"            VALUES (?, ?, ?, ?, ?)","highlight_start":1,"highlight_end":35},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            payment.project_id,","highlight_start":1,"highlight_end":32},{"text":"            payment.payment_type,","highlight_start":1,"highlight_end":34},{"text":"            payment.amount,","highlight_start":1,"highlight_end":28},{"text":"            payment.status,","highlight_start":1,"highlight_end":28},{"text":"            invoice_number","highlight_start":1,"highlight_end":27},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:225:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            INSERT INTO payments (project_id, payment_type, amount, status, invoice_number)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m228\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            VALUES (?, ?, ?, ?, ?)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            invoice_number\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m235\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":10415,"byte_end":10608,"line_start":252,"line_end":257,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        sqlx::query!(","highlight_start":9,"highlight_end":22},{"text":"            \"UPDATE payments SET status = ?, payment_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?\",","highlight_start":1,"highlight_end":109},{"text":"            status,","highlight_start":1,"highlight_end":20},{"text":"            payment_date,","highlight_start":1,"highlight_end":26},{"text":"            id","highlight_start":1,"highlight_end":15},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:252:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"UPDATE payments SET status = ?, payment_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            status,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            payment_date,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":12076,"byte_end":12464,"line_start":293,"line_end":303,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let result = sqlx::query!(","highlight_start":22,"highlight_end":35},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            INSERT INTO transactions (description, transaction_type, category, amount, status, reference)","highlight_start":1,"highlight_end":106},{"text":"            VALUES (?, ?, ?, ?, 'completed', ?)","highlight_start":1,"highlight_end":48},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            transaction.description,","highlight_start":1,"highlight_end":37},{"text":"            transaction.transaction_type,","highlight_start":1,"highlight_end":42},{"text":"            transaction.category,","highlight_start":1,"highlight_end":34},{"text":"            transaction.amount,","highlight_start":1,"highlight_end":32},{"text":"            transaction.reference","highlight_start":1,"highlight_end":34},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:293:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            INSERT INTO transactions (description, transaction_type, category, amount, status, reference)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            VALUES (?, ?, ?, ?, 'completed', ?)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            transaction.reference\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30592,"byte_end":30675,"line_start":674,"line_end":674,"column_start":9,"column_end":92,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30592,"byte_end":30675,"line_start":674,"line_end":674,"column_start":9,"column_end":92,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":13215,"byte_end":13422,"line_start":323,"line_end":326,"column_start":32,"column_end":14,"is_primary":false,"text":[{"text":"            let balance: f64 = sqlx::query_scalar!(","highlight_start":32,"highlight_end":52},{"text":"                \"SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) FROM transactions WHERE category = ?\",","highlight_start":1,"highlight_end":148},{"text":"                category","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_scalar!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30417,"byte_end":30442,"line_start":669,"line_end":669,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! query_scalar (","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:323:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   let balance: f64 = sqlx::query_scalar!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m324\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       \"SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) FROM transactions WHERE category =\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       category\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m326\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_______^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30592,"byte_end":30675,"line_start":674,"line_end":674,"column_start":9,"column_end":92,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30592,"byte_end":30675,"line_start":674,"line_end":674,"column_start":9,"column_end":92,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":13900,"byte_end":14053,"line_start":343,"line_end":347,"column_start":26,"column_end":10,"is_primary":false,"text":[{"text":"        let count: i64 = sqlx::query_scalar!(","highlight_start":26,"highlight_end":46},{"text":"            \"SELECT COUNT(*) FROM projects WHERE created_at BETWEEN ? AND ?\",","highlight_start":1,"highlight_end":78},{"text":"            start_date,","highlight_start":1,"highlight_end":24},{"text":"            end_date","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_scalar!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30417,"byte_end":30442,"line_start":669,"line_end":669,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! query_scalar (","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:343:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let count: i64 = sqlx::query_scalar!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m344\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"SELECT COUNT(*) FROM projects WHERE created_at BETWEEN ? AND ?\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m345\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            start_date,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m346\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            end_date\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30592,"byte_end":30675,"line_start":674,"line_end":674,"column_start":9,"column_end":92,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30592,"byte_end":30675,"line_start":674,"line_end":674,"column_start":9,"column_end":92,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database\\mod.rs","byte_start":14366,"byte_end":14535,"line_start":356,"line_end":360,"column_start":26,"column_end":10,"is_primary":false,"text":[{"text":"        let total: f64 = sqlx::query_scalar!(","highlight_start":26,"highlight_end":46},{"text":"            \"SELECT COALESCE(SUM(amount), 0) FROM payments WHERE created_at BETWEEN ? AND ?\",","highlight_start":1,"highlight_end":94},{"text":"            start_date,","highlight_start":1,"highlight_end":24},{"text":"            end_date","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_scalar!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":30417,"byte_end":30442,"line_start":669,"line_end":669,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! query_scalar (","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:356:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m356\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let total: f64 = sqlx::query_scalar!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m357\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"SELECT COALESCE(SUM(amount), 0) FROM payments WHERE created_at BETWEEN ? AND ?\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            start_date,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            end_date\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `SqlitePool` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\database\\mod.rs","byte_start":63,"byte_end":73,"line_start":4,"line_end":4,"column_start":15,"column_end":25,"is_primary":true,"text":[{"text":"    pub pool: SqlitePool,","highlight_start":15,"highlight_end":25}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this type alias","code":null,"level":"help","spans":[{"file_name":"src\\database\\mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::Result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use sqlx::SqlitePool;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `SqlitePool` in this scope\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:4:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub pool: SqlitePool,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this type alias\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ use sqlx::SqlitePool;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of undeclared type `Sqlite`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\database\\mod.rs","byte_start":620,"byte_end":626,"line_start":20,"line_end":20,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {","highlight_start":13,"highlight_end":19}],"label":"use of undeclared type `Sqlite`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src\\database\\mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::Result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use sqlx::Sqlite;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of undeclared type `Sqlite`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:20:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of undeclared type `Sqlite`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use sqlx::Sqlite;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of undeclared type `Sqlite`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\database\\mod.rs","byte_start":690,"byte_end":696,"line_start":21,"line_end":21,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"            Sqlite::create_database(&db_url).await?;","highlight_start":13,"highlight_end":19}],"label":"use of undeclared type `Sqlite`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src\\database\\mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::Result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use sqlx::Sqlite;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of undeclared type `Sqlite`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:21:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Sqlite::create_database(&db_url).await?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of undeclared type `Sqlite`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use sqlx::Sqlite;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of undeclared type `SqlitePool`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\database\\mod.rs","byte_start":761,"byte_end":771,"line_start":24,"line_end":24,"column_start":20,"column_end":30,"is_primary":true,"text":[{"text":"        let pool = SqlitePool::connect(&db_url).await?;","highlight_start":20,"highlight_end":30}],"label":"use of undeclared type `SqlitePool`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this type alias","code":null,"level":"help","spans":[{"file_name":"src\\database\\mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::Result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use sqlx::SqlitePool;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of undeclared type `SqlitePool`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:24:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let pool = SqlitePool::connect(&db_url).await?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of undeclared type `SqlitePool`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this type alias\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use sqlx::SqlitePool;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `SqlitePool` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\database\\mod.rs","byte_start":958,"byte_end":968,"line_start":32,"line_end":32,"column_start":38,"column_end":48,"is_primary":true,"text":[{"text":"    pub async fn get_pool(&self) -> &SqlitePool {","highlight_start":38,"highlight_end":48}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this type alias","code":null,"level":"help","spans":[{"file_name":"src\\database\\mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::error::Result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use sqlx::SqlitePool;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `SqlitePool` in this scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:32:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_pool(&self) -> &SqlitePool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this type alias\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use sqlx::SqlitePool;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json::Value`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\materials.rs","byte_start":81,"byte_end":98,"line_start":3,"line_end":3,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use serde_json::Value;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\materials.rs","byte_start":77,"byte_end":100,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::Value;","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `serde_json::Value`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\materials.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::Value;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\dashboard.rs","byte_start":90,"byte_end":98,"line_start":3,"line_end":3,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\commands\\dashboard.rs","byte_start":100,"byte_end":103,"line_start":3,"line_end":3,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\dashboard.rs","byte_start":77,"byte_end":106,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DateTime` and `Utc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\dashboard.rs:3:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `backup_path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\database\\mod.rs","byte_start":15136,"byte_end":15147,"line_start":378,"line_end":378,"column_start":40,"column_end":51,"is_primary":true,"text":[{"text":"    pub async fn restore_backup(&self, backup_path: String) -> Result<bool> {","highlight_start":40,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\database\\mod.rs","byte_start":15136,"byte_end":15147,"line_start":378,"line_end":378,"column_start":40,"column_end":51,"is_primary":true,"text":[{"text":"    pub async fn restore_backup(&self, backup_path: String) -> Result<bool> {","highlight_start":40,"highlight_end":51}],"label":null,"suggested_replacement":"_backup_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `backup_path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:378:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m378\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn restore_backup(&self, backup_path: String) -> Result<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_backup_path`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 14 previous errors; 3 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 14 previous errors; 3 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0412, E0433.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0412, E0433.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0412`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0412`.\u001b[0m\n"}
