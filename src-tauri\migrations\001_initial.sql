-- Materials table
CREATE TABLE materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    current_stock REAL NOT NULL DEFAULT 0,
    min_stock REAL NOT NULL DEFAULT 0,
    max_stock REAL NOT NULL DEFAULT 0,
    unit TEXT NOT NULL,
    cost_per_unit REAL NOT NULL DEFAULT 0,
    supplier TEXT NOT NULL,
    last_restocked DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Customers table
CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    address TEXT,
    customer_type TEXT NOT NULL CHECK (customer_type IN ('عائلي', 'تجاري')),
    status TEXT NOT NULL DEFAULT 'نشط' CHECK (status IN ('نشط', 'غير نشط', 'محتمل')),
    total_projects INTEGER NOT NULL DEFAULT 0,
    total_spent REAL NOT NULL DEFAULT 0,
    discount REAL NOT NULL DEFAULT 0,
    discount_reason TEXT,
    registration_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_interaction DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Employees table
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    position TEXT NOT NULL,
    department TEXT NOT NULL,
    hourly_rate REAL NOT NULL DEFAULT 0,
    hours_worked REAL NOT NULL DEFAULT 0,
    overtime REAL NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'نشط' CHECK (status IN ('نشط', 'إجازة', 'غير نشط')),
    vacation_days INTEGER NOT NULL DEFAULT 0,
    allowances REAL NOT NULL DEFAULT 0,
    deductions REAL NOT NULL DEFAULT 0,
    social_insurance REAL NOT NULL DEFAULT 0,
    discount REAL NOT NULL DEFAULT 0,
    discount_reason TEXT,
    join_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Projects table
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    customer_id INTEGER NOT NULL,
    assigned_employee_id INTEGER,
    furniture_type TEXT NOT NULL,
    square_meters REAL NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'في انتظار الموافقة' CHECK (status IN ('في انتظار الموافقة', 'في الإنتاج', 'مكتمل', 'ملغي')),
    stage TEXT NOT NULL DEFAULT 'أولي' CHECK (stage IN ('أولي', 'تصميم', 'تصنيع', 'تشطيب', 'مكتمل')),
    total_amount REAL NOT NULL DEFAULT 0,
    first_payment REAL NOT NULL DEFAULT 0,
    final_payment REAL NOT NULL DEFAULT 0,
    materials_cost REAL NOT NULL DEFAULT 0,
    labor_cost REAL NOT NULL DEFAULT 0,
    designer_fee REAL NOT NULL DEFAULT 0,
    factory_overhead REAL NOT NULL DEFAULT 0,
    profit_margin REAL NOT NULL DEFAULT 0,
    start_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expected_completion DATETIME NOT NULL,
    actual_completion DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_employee_id) REFERENCES employees (id) ON DELETE SET NULL
);

-- Project materials junction table
CREATE TABLE project_materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    material_id INTEGER NOT NULL,
    quantity_needed REAL NOT NULL DEFAULT 0,
    cost REAL NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
    FOREIGN KEY (material_id) REFERENCES materials (id) ON DELETE CASCADE
);

-- Project accessories table
CREATE TABLE project_accessories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    accessory_type TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 0,
    cost_per_unit REAL NOT NULL DEFAULT 0,
    total_cost REAL NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    payment_type TEXT NOT NULL CHECK (payment_type IN ('أولية', 'تقدم', 'نهائية')),
    amount REAL NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'معلق' CHECK (status IN ('معلق', 'مدفوع', 'متأخر')),
    invoice_number TEXT NOT NULL UNIQUE,
    payment_date DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
);

-- Transactions table
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    description TEXT NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('income', 'expense')),
    category TEXT NOT NULL,
    amount REAL NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'مكتمل' CHECK (status IN ('معلق', 'مكتمل', 'ملغي')),
    reference TEXT,
    project_id INTEGER,
    transaction_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE SET NULL
);

-- Activities table for tracking system activities
CREATE TABLE activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    activity_type TEXT NOT NULL,
    entity_type TEXT,
    entity_id INTEGER,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Accounts table for treasury management
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    account_type TEXT NOT NULL CHECK (account_type IN ('جاري', 'توفير', 'استثمار')),
    balance REAL NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'د.ل',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Backups table for tracking backup files
CREATE TABLE backups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    backup_type TEXT NOT NULL CHECK (backup_type IN ('manual', 'automatic')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_materials_category ON materials(category);
CREATE INDEX idx_materials_stock ON materials(current_stock, min_stock);
CREATE INDEX idx_customers_type ON customers(customer_type);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_employees_department ON employees(department);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_customer ON projects(customer_id);
CREATE INDEX idx_projects_employee ON projects(assigned_employee_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_project ON payments(project_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_activities_type ON activities(activity_type);
CREATE INDEX idx_activities_date ON activities(created_at);

-- Insert default accounts
INSERT INTO accounts (name, account_type, balance) VALUES
('الحساب التشغيلي الرئيسي', 'جاري', 0.0),
('حساب الرواتب', 'جاري', 0.0),
('صندوق الطوارئ', 'توفير', 0.0),
('صندوق المعدات', 'توفير', 0.0);
