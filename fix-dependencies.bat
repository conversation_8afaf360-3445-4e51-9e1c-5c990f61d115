@echo off
echo ========================================
echo   إصلاح تبعيات المشروع وبناء التطبيق
echo ========================================
echo.

echo الخطوة 1: تنظيف الملفات القديمة...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist out rmdir /s /q out
echo تم تنظيف الملفات القديمة.
echo.

echo الخطوة 2: تثبيت التبعيات مع تجاهل التعارضات...
npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo خطأ في تثبيت التبعيات!
    pause
    exit /b 1
)
echo تم تثبيت التبعيات بنجاح.
echo.

echo الخطوة 3: بناء تطبيق Next.js...
npm run build
if %errorlevel% neq 0 (
    echo خطأ في بناء Next.js!
    pause
    exit /b 1
)
echo تم بناء Next.js بنجاح.
echo.

echo الخطوة 4: التحقق من وجود Rust...
rustc --version
if %errorlevel% neq 0 (
    echo Rust غير مثبت! يرجى تثبيت Rust من https://rustup.rs/
    pause
    exit /b 1
)
echo Rust مثبت ومتاح.
echo.

echo الخطوة 5: بناء تطبيق Tauri...
npm run tauri:build
if %errorlevel% neq 0 (
    echo خطأ في بناء Tauri!
    echo جرب تشغيل: npm run tauri:build:debug
    pause
    exit /b 1
)
echo.

echo ========================================
echo   تم بناء التطبيق بنجاح! 🎉
echo ========================================
echo.
echo ملفات التطبيق موجودة في:
echo - Windows: src-tauri\target\release\bundle\msi\
echo - Executable: src-tauri\target\release\
echo.
echo لتشغيل التطبيق في وضع التطوير:
echo npm run tauri:dev
echo.
pause
