#!/usr/bin/env python3
"""
Simple icon generator using PIL only
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """Create a simple icon using PIL"""
    # Create image with blue background
    img = Image.new('RGBA', (size, size), (30, 64, 175, 255))  # Blue background
    draw = ImageDraw.Draw(img)
    
    # Draw white circle in center
    margin = size // 8
    circle_size = size - 2 * margin
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                fill=(255, 255, 255, 200))
    
    # Draw "H" in center
    try:
        # Try to use a font
        font_size = size // 3
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    # Get text size and center it
    text = "H"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill=(30, 64, 175, 255), font=font)
    
    # Add some decorative elements
    # Small circles
    circle_radius = size // 20
    positions = [
        (size // 4, size // 4),
        (3 * size // 4, size // 4),
        (size // 4, 3 * size // 4),
        (3 * size // 4, 3 * size // 4)
    ]
    
    colors = [(251, 191, 36, 180), (220, 38, 38, 180), (5, 150, 105, 180), (168, 85, 247, 180)]
    
    for i, (x, y) in enumerate(positions):
        color = colors[i % len(colors)]
        draw.ellipse([x - circle_radius, y - circle_radius, 
                     x + circle_radius, y + circle_radius], fill=color)
    
    img.save(output_path)
    print(f"Created: {output_path}")

def create_ico_file(png_path, ico_path):
    """Create ICO file from PNG"""
    img = Image.open(png_path)
    img.save(ico_path, format='ICO', sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)])
    print(f"Created: {ico_path}")

def main():
    icons_dir = "src-tauri/icons"
    
    # Create icons directory if it doesn't exist
    os.makedirs(icons_dir, exist_ok=True)
    
    # Generate PNG files
    sizes = [32, 128, 256, 512]
    png_files = []
    
    for size in sizes:
        png_file = f"{icons_dir}/{size}x{size}.png"
        create_icon(size, png_file)
        png_files.append(png_file)
        
        # Special case for 128x128@2x
        if size == 128:
            png_2x_file = f"{icons_dir}/<EMAIL>"
            create_icon(256, png_2x_file)
    
    # Create main icon.png
    main_icon = f"{icons_dir}/icon.png"
    create_icon(128, main_icon)
    
    # Create ICO file for Windows
    ico_file = f"{icons_dir}/icon.ico"
    create_ico_file(f"{icons_dir}/256x256.png", ico_file)
    
    # Create a simple ICNS file (just copy the PNG for now)
    icns_file = f"{icons_dir}/icon.icns"
    try:
        img = Image.open(f"{icons_dir}/512x512.png")
        img.save(icns_file, format='ICNS')
        print(f"Created: {icns_file}")
    except Exception as e:
        print(f"Could not create ICNS: {e}")
        # Just copy the PNG file
        import shutil
        shutil.copy(f"{icons_dir}/512x512.png", icns_file)
        print(f"Copied PNG as ICNS: {icns_file}")
    
    print("Icon generation complete!")

if __name__ == "__main__":
    main()
