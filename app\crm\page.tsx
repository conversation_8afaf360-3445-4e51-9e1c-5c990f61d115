"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Users, ArrowLeft, Phone, Mail, Calendar, Eye, Edit, Trash2, Percent, UserPlus, TrendingUp } from "lucide-react"
import Link from "next/link"

export default function CRMManagement() {
  const [customers, setCustomers] = useState([
    {
      id: 1,
      name: "عائلة الأحمد",
      email: "<EMAIL>",
      phone: "+218 91 234 5678",
      address: "شارع الجمهورية، طرابلس",
      registrationDate: "2024-01-10",
      totalProjects: 2,
      totalSpent: 6162.5, // LYD
      status: "نشط",
      lastInteraction: "2024-01-25",
      customerType: "عائلي",
      discount: 0,
      discountReason: "",
    },
    {
      id: 2,
      name: "شركة التقنية المتقدمة",
      email: "<EMAIL>",
      phone: "+218 21 345 6789",
      address: "المنطقة الصناعية، مصراتة",
      registrationDate: "2024-01-05",
      totalProjects: 1,
      totalSpent: 2225.0, // LYD
      status: "نشط",
      lastInteraction: "2024-01-30",
      customerType: "تجاري",
      discount: 0,
      discountReason: "",
    },
    {
      id: 3,
      name: "منزل العائلة",
      email: "<EMAIL>",
      phone: "+218 92 456 7890",
      address: "حي الأندلس، بنغازي",
      registrationDate: "2023-12-20",
      totalProjects: 1,
      totalSpent: 3100.0, // LYD
      status: "نشط",
      lastInteraction: "2024-02-01",
      customerType: "عائلي",
      discount: 155.0, // 5% discount applied
      discountReason: "عميل مميز",
    },
  ])

  const [newCustomer, setNewCustomer] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    customerType: "",
  })

  const [editingCustomer, setEditingCustomer] = useState(null)
  const [discountCustomer, setDiscountCustomer] = useState(null)
  const [discountAmount, setDiscountAmount] = useState("")
  const [discountReason, setDiscountReason] = useState("")

  const addCustomer = () => {
    const customer = {
      id: customers.length + 1,
      ...newCustomer,
      registrationDate: new Date().toISOString().split("T")[0],
      totalProjects: 0,
      totalSpent: 0,
      status: "نشط",
      lastInteraction: new Date().toISOString().split("T")[0],
      discount: 0,
      discountReason: "",
    }
    setCustomers([...customers, customer])
    setNewCustomer({
      name: "",
      email: "",
      phone: "",
      address: "",
      customerType: "",
    })
  }

  const updateCustomer = () => {
    setCustomers(customers.map((customer) => (customer.id === editingCustomer.id ? editingCustomer : customer)))
    setEditingCustomer(null)
  }

  const deleteCustomer = (id) => {
    setCustomers(customers.filter((customer) => customer.id !== id))
  }

  const applyDiscount = () => {
    const discount = Number.parseFloat(discountAmount)
    setCustomers(
      customers.map((customer) =>
        customer.id === discountCustomer.id
          ? {
              ...customer,
              discount: discount,
              discountReason: discountReason,
            }
          : customer,
      ),
    )
    setDiscountCustomer(null)
    setDiscountAmount("")
    setDiscountReason("")
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "نشط":
        return "bg-emerald-100 text-emerald-700 border-emerald-200"
      case "غير نشط":
        return "bg-slate-100 text-slate-700 border-slate-200"
      case "محتمل":
        return "bg-amber-100 text-amber-700 border-amber-200"
      default:
        return "bg-slate-100 text-slate-700 border-slate-200"
    }
  }

  const getCustomerTypeColor = (type) => {
    switch (type) {
      case "تجاري":
        return "bg-indigo-100 text-indigo-700 border-indigo-200"
      case "عائلي":
        return "bg-purple-100 text-purple-700 border-purple-200"
      default:
        return "bg-slate-100 text-slate-700 border-slate-200"
    }
  }

  const getTotalCustomers = () => customers.length
  const getActiveCustomers = () => customers.filter((c) => c.status === "نشط").length
  const getTotalRevenue = () => customers.reduce((total, customer) => total + customer.totalSpent, 0)
  const getAverageOrderValue = () => {
    const totalProjects = customers.reduce((total, customer) => total + customer.totalProjects, 0)
    return totalProjects > 0 ? getTotalRevenue() / totalProjects : 0
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/">
              <Button variant="outline" size="sm" className="modern-btn-secondary border-slate-300">
                <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
                العودة للوحة التحكم
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                إدارة علاقات العملاء
              </h1>
              <p className="text-slate-600">نظام شامل لإدارة العملاء وتتبع التفاعلات بالدينار الليبي (د.ل)</p>
            </div>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button className="modern-btn-primary">
                <UserPlus className="h-4 w-4 ml-2" />
                إضافة عميل جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="modern-card border-0">
              <DialogHeader>
                <DialogTitle className="text-slate-900 text-xl">إضافة عميل جديد</DialogTitle>
                <DialogDescription className="text-slate-600">أدخل تفاصيل العميل الجديد</DialogDescription>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                <div>
                  <Label htmlFor="name" className="text-slate-700 font-medium">
                    اسم العميل
                  </Label>
                  <Input
                    id="name"
                    value={newCustomer.name}
                    onChange={(e) => setNewCustomer({ ...newCustomer, name: e.target.value })}
                    placeholder="اسم العميل أو الشركة"
                    className="modern-input mt-2"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email" className="text-slate-700 font-medium">
                      البريد الإلكتروني
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={newCustomer.email}
                      onChange={(e) => setNewCustomer({ ...newCustomer, email: e.target.value })}
                      placeholder="<EMAIL>"
                      className="modern-input mt-2"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-slate-700 font-medium">
                      رقم الهاتف
                    </Label>
                    <Input
                      id="phone"
                      value={newCustomer.phone}
                      onChange={(e) => setNewCustomer({ ...newCustomer, phone: e.target.value })}
                      placeholder="+218 91 234 5678"
                      className="modern-input mt-2"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="address" className="text-slate-700 font-medium">
                    العنوان
                  </Label>
                  <Input
                    id="address"
                    value={newCustomer.address}
                    onChange={(e) => setNewCustomer({ ...newCustomer, address: e.target.value })}
                    placeholder="العنوان الكامل"
                    className="modern-input mt-2"
                  />
                </div>
                <div>
                  <Label htmlFor="customerType" className="text-slate-700 font-medium">
                    نوع العميل
                  </Label>
                  <select
                    id="customerType"
                    value={newCustomer.customerType}
                    onChange={(e) => setNewCustomer({ ...newCustomer, customerType: e.target.value })}
                    className="modern-input mt-2 w-full"
                  >
                    <option value="">اختر نوع العميل</option>
                    <option value="عائلي">عائلي</option>
                    <option value="تجاري">تجاري</option>
                  </select>
                </div>
                <Button onClick={addCustomer} className="modern-btn-primary">
                  إضافة العميل
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">+12%</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">إجمالي العملاء</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{getTotalCustomers()}</div>
              <p className="text-sm text-slate-500">عملاء مسجلين</p>
            </div>
          </div>

          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-emerald-500 to-teal-600 shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">+5</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">العملاء النشطون</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{getActiveCustomers()}</div>
              <p className="text-sm text-slate-500">عملاء نشطون</p>
            </div>
          </div>

          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-amber-500 to-orange-600 shadow-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">+18%</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">إجمالي الإيرادات</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{getTotalRevenue().toLocaleString()} د.ل</div>
              <p className="text-sm text-slate-500">من جميع العملاء</p>
            </div>
          </div>

          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-600 shadow-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">+8%</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">متوسط قيمة الطلب</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{getAverageOrderValue().toFixed(2)} د.ل</div>
              <p className="text-sm text-slate-500">لكل مشروع</p>
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <div className="modern-table">
          <div className="p-6 border-b border-slate-200">
            <h2 className="text-xl font-bold text-slate-900">قائمة العملاء</h2>
            <p className="text-slate-600 mt-1">إدارة شاملة لجميع العملاء وتفاصيلهم بالدينار الليبي</p>
          </div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="modern-table th">اسم العميل</TableHead>
                  <TableHead className="modern-table th">نوع العميل</TableHead>
                  <TableHead className="modern-table th">معلومات الاتصال</TableHead>
                  <TableHead className="modern-table th">المشاريع</TableHead>
                  <TableHead className="modern-table th">إجمالي الإنفاق</TableHead>
                  <TableHead className="modern-table th">الخصم</TableHead>
                  <TableHead className="modern-table th">الحالة</TableHead>
                  <TableHead className="modern-table th">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customers.map((customer) => (
                  <TableRow key={customer.id} className="modern-table tr">
                    <TableCell className="modern-table td">
                      <div className="font-semibold text-slate-900">{customer.name}</div>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <Badge className={`${getCustomerTypeColor(customer.customerType)} border rounded-full px-3 py-1`}>
                        {customer.customerType}
                      </Badge>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2 space-x-reverse text-sm">
                          <Phone className="h-3 w-3 text-slate-500" />
                          <span className="text-slate-600">{customer.phone}</span>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse text-sm">
                          <Mail className="h-3 w-3 text-slate-500" />
                          <span className="text-slate-600">{customer.email}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <div className="font-semibold text-slate-900">{customer.totalProjects}</div>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <div className="font-semibold text-emerald-600">{customer.totalSpent.toLocaleString()} د.ل</div>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <div className="text-amber-600 font-medium">
                        {customer.discount > 0 ? `${customer.discount.toFixed(2)} د.ل` : "-"}
                      </div>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <Badge className={`${getStatusColor(customer.status)} border rounded-full px-3 py-1`}>
                        {customer.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="modern-table td">
                      <div className="flex space-x-2 space-x-reverse">
                        <Link href={`/crm/${customer.id}`}>
                          <Button
                            size="sm"
                            variant="outline"
                            className="rounded-xl border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        </Link>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingCustomer({ ...customer })}
                              className="rounded-xl border-slate-200 text-slate-600 hover:bg-slate-50"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="modern-card border-0">
                            <DialogHeader>
                              <DialogTitle className="text-slate-900">تعديل بيانات العميل</DialogTitle>
                            </DialogHeader>
                            {editingCustomer && (
                              <div className="grid gap-4 py-4">
                                <div>
                                  <Label className="text-slate-700 font-medium">اسم العميل</Label>
                                  <Input
                                    value={editingCustomer.name}
                                    onChange={(e) => setEditingCustomer({ ...editingCustomer, name: e.target.value })}
                                    className="modern-input mt-2"
                                  />
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <Label className="text-slate-700 font-medium">البريد الإلكتروني</Label>
                                    <Input
                                      value={editingCustomer.email}
                                      onChange={(e) =>
                                        setEditingCustomer({ ...editingCustomer, email: e.target.value })
                                      }
                                      className="modern-input mt-2"
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-slate-700 font-medium">رقم الهاتف</Label>
                                    <Input
                                      value={editingCustomer.phone}
                                      onChange={(e) =>
                                        setEditingCustomer({ ...editingCustomer, phone: e.target.value })
                                      }
                                      className="modern-input mt-2"
                                    />
                                  </div>
                                </div>
                                <div>
                                  <Label className="text-slate-700 font-medium">العنوان</Label>
                                  <Input
                                    value={editingCustomer.address}
                                    onChange={(e) =>
                                      setEditingCustomer({ ...editingCustomer, address: e.target.value })
                                    }
                                    className="modern-input mt-2"
                                  />
                                </div>
                                <Button onClick={updateCustomer} className="modern-btn-primary">
                                  حفظ التغييرات
                                </Button>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setDiscountCustomer(customer)}
                              className="rounded-xl border-amber-200 text-amber-600 hover:bg-amber-50"
                            >
                              <Percent className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="modern-card border-0">
                            <DialogHeader>
                              <DialogTitle className="text-slate-900">تطبيق خصم للعميل</DialogTitle>
                              <DialogDescription>تطبيق خصم على حساب {customer.name}</DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              <div>
                                <Label className="text-slate-700 font-medium">مبلغ الخصم (د.ل)</Label>
                                <Input
                                  type="number"
                                  step="0.01"
                                  value={discountAmount}
                                  onChange={(e) => setDiscountAmount(e.target.value)}
                                  placeholder="0.00"
                                  className="modern-input mt-2"
                                />
                              </div>
                              <div>
                                <Label className="text-slate-700 font-medium">سبب الخصم</Label>
                                <Input
                                  value={discountReason}
                                  onChange={(e) => setDiscountReason(e.target.value)}
                                  placeholder="سبب الخصم"
                                  className="modern-input mt-2"
                                />
                              </div>
                              <Button
                                onClick={applyDiscount}
                                className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white rounded-xl px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                              >
                                تطبيق الخصم
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              className="rounded-xl border-red-200 text-red-600 hover:bg-red-50"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="modern-card border-0">
                            <AlertDialogHeader>
                              <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                              <AlertDialogDescription>
                                هل أنت متأكد من حذف العميل {customer.name}؟ هذا الإجراء لا يمكن التراجع عنه.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="modern-btn-secondary">إلغاء</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => deleteCustomer(customer.id)}
                                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                              >
                                حذف
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  )
}
