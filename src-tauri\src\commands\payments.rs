use crate::database::Database;
use crate::models::{Payment, CreatePayment};
use crate::error::Result;
use tauri::State;

#[tauri::command]
pub async fn get_payments(db: State<'_, Database>) -> Result<Vec<Payment>> {
    db.get_payments().await
}

#[tauri::command]
pub async fn create_payment(db: State<'_, Database>, payment: CreatePayment) -> Result<Payment> {
    db.create_payment(payment).await
}

#[tauri::command]
pub async fn update_payment_status(db: State<'_, Database>, id: i64, status: String) -> Result<Payment> {
    db.update_payment_status(id, status).await
}

#[tauri::command]
pub async fn generate_invoice(db: State<'_, Database>, payment_id: i64) -> Result<String> {
    db.generate_invoice(payment_id).await
}
