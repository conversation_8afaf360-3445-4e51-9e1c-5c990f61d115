"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Package,
  Plus,
  ArrowLeft,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Upload,
  Download,
  FileSpreadsheet,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react"
import Link from "next/link"
import { useTauriQuery, useTauriMutation } from "@/hooks/use-tauri-api"
import { materialsApi, fileApi, notificationApi, type CreateMaterial } from "@/lib/tauri-api"
import { OfflineIndicator } from "@/components/ui/offline-indicator"

export default function MaterialsManagement() {
  const { data: materials = [], loading, error, refetch } = useTauriQuery(materialsApi.getAll)

  const [newMaterial, setNewMaterial] = useState<CreateMaterial>({
    name: "",
    category: "",
    current_stock: 0,
    min_stock: 0,
    max_stock: 0,
    unit: "",
    cost_per_unit: 0,
    supplier: "",
  })

  const [importResults, setImportResults] = useState<any>(null)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)

  const createMutation = useTauriMutation(materialsApi.create, () => {
    notificationApi.success("تم بنجاح", "تم إضافة المادة بنجاح")
    setShowAddDialog(false)
    setNewMaterial({
      name: "",
      category: "",
      current_stock: 0,
      min_stock: 0,
      max_stock: 0,
      unit: "",
      cost_per_unit: 0,
      supplier: "",
    })
    refetch()
  })

  const deleteMutation = useTauriMutation(materialsApi.delete, () => {
    notificationApi.success("تم بنجاح", "تم حذف المادة بنجاح")
    refetch()
  })

  const exportMutation = useTauriMutation(materialsApi.export, async (csvContent: string) => {
    await fileApi.saveFile(csvContent, "materials_export.csv")
  })

  const getStockStatus = (current: number, min: number, max: number) => {
    if (current <= min) return { status: "منخفض", color: "bg-red-100 text-red-800 border-red-200" }
    if (current >= max * 0.8) return { status: "مرتفع", color: "bg-amber-100 text-amber-800 border-amber-200" }
    return { status: "طبيعي", color: "bg-emerald-100 text-emerald-800 border-emerald-200" }
  }

  const getTotalValue = () => {
    return materials.reduce((total, material) => total + material.current_stock * material.cost_per_unit, 0)
  }

  const getLowStockCount = () => {
    return materials.filter((material) => material.current_stock <= material.min_stock).length
  }

  const handleAddMaterial = () => {
    createMutation.mutate(newMaterial)
  }

  const handleExport = () => {
    exportMutation.mutate()
  }

  const handleImport = async () => {
    const file = await fileApi.openFile([
      { name: "CSV Files", extensions: ["csv"] },
      { name: "Excel Files", extensions: ["xlsx", "xls"] },
    ])

    if (file) {
      try {
        const lines = file.content.split("\n")
        const dataLines = lines.slice(1).filter((line) => line.trim())

        const importedMaterials: CreateMaterial[] = []
        const errors: string[] = []

        dataLines.forEach((line, index) => {
          try {
            const values = line.split(",").map((val) => val.replace(/"/g, "").trim())

            if (values.length >= 8) {
              const material: CreateMaterial = {
                name: values[0],
                category: values[1],
                current_stock: Number.parseFloat(values[2]) || 0,
                min_stock: Number.parseFloat(values[3]) || 0,
                max_stock: Number.parseFloat(values[4]) || 0,
                unit: values[5],
                cost_per_unit: Number.parseFloat(values[6]) || 0,
                supplier: values[7],
              }

              if (material.name && material.category && material.unit && material.supplier) {
                importedMaterials.push(material)
              } else {
                errors.push(`الصف ${index + 2}: بيانات ناقصة`)
              }
            } else {
              errors.push(`الصف ${index + 2}: عدد أعمدة غير صحيح`)
            }
          } catch (error) {
            errors.push(`الصف ${index + 2}: خطأ في تحليل البيانات`)
          }
        })

        setImportResults({
          success: importedMaterials.length,
          errors: errors,
          materials: importedMaterials,
        })
        setShowImportDialog(true)
      } catch (error) {
        notificationApi.error("خطأ", "خطأ في قراءة الملف")
      }
    }
  }

  const confirmImport = async () => {
    if (importResults && importResults.materials.length > 0) {
      try {
        await materialsApi.import(importResults.materials)
        notificationApi.success("تم بنجاح", `تم استيراد ${importResults.materials.length} مادة بنجاح`)
        setImportResults(null)
        setShowImportDialog(false)
        refetch()
      } catch (error) {
        notificationApi.error("خطأ", "فشل في استيراد المواد")
      }
    }
  }

  const downloadTemplate = async () => {
    const templateData = [
      ["اسم المادة", "الفئة", "المخزون الحالي", "الحد الأدنى", "الحد الأقصى", "الوحدة", "التكلفة لكل وحدة", "المورد"],
      ["مثال: ألواح خشب البلوط", "خشب", "100", "20", "200", "م²", "45.50", "شركة الخشب المتقدمة"],
    ]

    const csvContent = templateData.map((row) => row.map((cell) => `"${cell}"`).join(",")).join("\n")
    await fileApi.saveFile(csvContent, "materials_template.csv")
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-indigo-600" />
          <p className="text-slate-600">جاري تحميل المواد...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-8 w-8 mx-auto mb-4 text-red-600" />
          <p className="text-red-600 mb-4">خطأ في تحميل البيانات: {error}</p>
          <Button onClick={refetch} variant="outline">
            إعادة المحاولة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/">
              <Button variant="outline" size="sm" className="modern-btn-secondary border-slate-300">
                <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
                العودة للوحة التحكم
              </Button>
            </Link>
            <div>
              <div className="flex items-center space-x-3 space-x-reverse mb-2">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  إدارة المواد
                </h1>
                <OfflineIndicator />
              </div>
              <p className="text-slate-600">تتبع وإدارة مخزون المواد الخام (قاعدة بيانات محلية)</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse bg-white rounded-2xl p-1 shadow-sm border border-slate-200">
              <Button
                onClick={downloadTemplate}
                variant="outline"
                size="sm"
                className="rounded-xl border-emerald-200 text-emerald-600 hover:bg-emerald-50"
              >
                <FileSpreadsheet className="h-4 w-4 ml-2" />
                تحميل النموذج
              </Button>

              <Button
                onClick={handleImport}
                variant="outline"
                size="sm"
                className="rounded-xl border-blue-200 text-blue-600 hover:bg-blue-50"
              >
                <Upload className="h-4 w-4 ml-2" />
                استيراد Excel
              </Button>

              <Button
                onClick={handleExport}
                variant="outline"
                size="sm"
                className="rounded-xl border-amber-200 text-amber-600 hover:bg-amber-50"
                disabled={exportMutation.loading}
              >
                {exportMutation.loading ? (
                  <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 ml-2" />
                )}
                تصدير Excel
              </Button>
            </div>

            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button className="modern-btn-primary">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة مادة
                </Button>
              </DialogTrigger>
              <DialogContent className="modern-card border-0">
                <DialogHeader>
                  <DialogTitle className="text-slate-900 text-xl">إضافة مادة جديدة</DialogTitle>
                  <DialogDescription className="text-slate-600">أدخل تفاصيل المادة الجديدة</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-slate-700 font-medium">
                        اسم المادة
                      </Label>
                      <Input
                        id="name"
                        value={newMaterial.name}
                        onChange={(e) => setNewMaterial({ ...newMaterial, name: e.target.value })}
                        placeholder="مثال: ألواح خشب البلوط"
                        className="modern-input mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="category" className="text-slate-700 font-medium">
                        الفئة
                      </Label>
                      <Select
                        value={newMaterial.category}
                        onValueChange={(value) => setNewMaterial({ ...newMaterial, category: value })}
                      >
                        <SelectTrigger className="modern-input mt-2">
                          <SelectValue placeholder="اختر الفئة" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="خشب">خشب</SelectItem>
                          <SelectItem value="أدوات">أدوات</SelectItem>
                          <SelectItem value="مواد لاصقة">مواد لاصقة</SelectItem>
                          <SelectItem value="تشطيبات">تشطيبات</SelectItem>
                          <SelectItem value="عدد">عدد</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="currentStock" className="text-slate-700 font-medium">
                        المخزون الحالي
                      </Label>
                      <Input
                        id="currentStock"
                        type="number"
                        value={newMaterial.current_stock}
                        onChange={(e) =>
                          setNewMaterial({ ...newMaterial, current_stock: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="100"
                        className="modern-input mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="minStock" className="text-slate-700 font-medium">
                        الحد الأدنى
                      </Label>
                      <Input
                        id="minStock"
                        type="number"
                        value={newMaterial.min_stock}
                        onChange={(e) =>
                          setNewMaterial({ ...newMaterial, min_stock: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="20"
                        className="modern-input mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="maxStock" className="text-slate-700 font-medium">
                        الحد الأقصى
                      </Label>
                      <Input
                        id="maxStock"
                        type="number"
                        value={newMaterial.max_stock}
                        onChange={(e) =>
                          setNewMaterial({ ...newMaterial, max_stock: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="500"
                        className="modern-input mt-2"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="unit" className="text-slate-700 font-medium">
                        الوحدة
                      </Label>
                      <Select
                        value={newMaterial.unit}
                        onValueChange={(value) => setNewMaterial({ ...newMaterial, unit: value })}
                      >
                        <SelectTrigger className="modern-input mt-2">
                          <SelectValue placeholder="اختر الوحدة" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="م²">م²</SelectItem>
                          <SelectItem value="قطعة">قطعة</SelectItem>
                          <SelectItem value="كيلو">كيلو</SelectItem>
                          <SelectItem value="لتر">لتر</SelectItem>
                          <SelectItem value="زجاجة">زجاجة</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="costPerUnit" className="text-slate-700 font-medium">
                        التكلفة لكل وحدة (د.ل)
                      </Label>
                      <Input
                        id="costPerUnit"
                        type="number"
                        step="0.01"
                        value={newMaterial.cost_per_unit}
                        onChange={(e) =>
                          setNewMaterial({ ...newMaterial, cost_per_unit: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="25.00"
                        className="modern-input mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="supplier" className="text-slate-700 font-medium">
                        المورد
                      </Label>
                      <Input
                        id="supplier"
                        value={newMaterial.supplier}
                        onChange={(e) => setNewMaterial({ ...newMaterial, supplier: e.target.value })}
                        placeholder="اسم المورد"
                        className="modern-input mt-2"
                      />
                    </div>
                  </div>
                  <Button onClick={handleAddMaterial} className="modern-btn-primary" disabled={createMutation.loading}>
                    {createMutation.loading ? (
                      <>
                        <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                        جاري الإضافة...
                      </>
                    ) : (
                      "إضافة المادة"
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Import Results Dialog */}
        <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
          <DialogContent className="modern-card border-0 max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-slate-900 text-xl flex items-center space-x-2 space-x-reverse">
                <FileSpreadsheet className="h-6 w-6 text-indigo-600" />
                <span>نتائج الاستيراد</span>
              </DialogTitle>
              <DialogDescription className="text-slate-600">مراجعة البيانات المستوردة قبل الحفظ</DialogDescription>
            </DialogHeader>

            {importResults && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <CheckCircle className="h-5 w-5 text-emerald-600" />
                      <span className="font-medium text-emerald-900">تم بنجاح</span>
                    </div>
                    <div className="text-2xl font-bold text-emerald-600 mt-2">{importResults.success}</div>
                    <p className="text-sm text-emerald-700">مادة جاهزة للإضافة</p>
                  </div>

                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <XCircle className="h-5 w-5 text-red-600" />
                      <span className="font-medium text-red-900">أخطاء</span>
                    </div>
                    <div className="text-2xl font-bold text-red-600 mt-2">{importResults.errors.length}</div>
                    <p className="text-sm text-red-700">صف يحتوي على أخطاء</p>
                  </div>
                </div>

                {importResults.errors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <h4 className="font-medium text-red-900 mb-2">الأخطاء المكتشفة:</h4>
                    <ul className="text-sm text-red-700 space-y-1">
                      {importResults.errors.map((error: string, index: number) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex justify-end space-x-3 space-x-reverse">
                  <Button variant="outline" onClick={() => setShowImportDialog(false)} className="modern-btn-secondary">
                    إلغاء
                  </Button>
                  {importResults.materials.length > 0 && (
                    <Button onClick={confirmImport} className="modern-btn-primary">
                      تأكيد الاستيراد ({importResults.materials.length} مادة)
                    </Button>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg">
                <Package className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">محلي</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">إجمالي المواد</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{materials.length}</div>
              <p className="text-sm text-slate-500">مواد نشطة</p>
            </div>
          </div>

          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-emerald-500 to-teal-600 shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">محفوظ محلياً</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">القيمة الإجمالية</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{getTotalValue().toFixed(2)} د.ل</div>
              <p className="text-sm text-slate-500">قيمة المخزون</p>
            </div>
          </div>

          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-amber-500 to-orange-600 shadow-lg">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingDown className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium text-red-600">تنبيه</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">مواد منخفضة المخزون</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">{getLowStockCount()}</div>
              <p className="text-sm text-slate-500">تحتاج إعادة تخزين</p>
            </div>
          </div>

          <div className="modern-stat-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-600 shadow-lg">
                <Package className="h-6 w-6 text-white" />
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium text-emerald-600">متنوع</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-slate-600 mb-1">الفئات</h3>
              <div className="text-3xl font-bold text-slate-900 mb-1">
                {[...new Set(materials.map((m) => m.category))].length}
              </div>
              <p className="text-sm text-slate-500">أنواع المواد</p>
            </div>
          </div>
        </div>

        {/* Materials Table */}
        {materials.length === 0 ? (
          <div className="modern-table">
            <div className="p-12 text-center">
              <Package className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <h3 className="text-lg font-semibold text-slate-900 mb-2">لا توجد مواد</h3>
              <p className="text-slate-600 mb-6">ابدأ بإضافة المواد الخام لمصنعك</p>
              <Button onClick={() => setShowAddDialog(true)} className="modern-btn-primary">
                <Plus className="h-4 w-4 ml-2" />
                إضافة أول مادة
              </Button>
            </div>
          </div>
        ) : (
          <div className="modern-table">
            <div className="p-6 border-b border-slate-200">
              <h2 className="text-xl font-bold text-slate-900">مخزون المواد</h2>
              <p className="text-slate-600 mt-1">قائمة شاملة بالمواد الخام ومستويات المخزون (محفوظة محلياً)</p>
            </div>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="modern-table th">اسم المادة</TableHead>
                    <TableHead className="modern-table th">الفئة</TableHead>
                    <TableHead className="modern-table th">المخزون الحالي</TableHead>
                    <TableHead className="modern-table th">حالة المخزون</TableHead>
                    <TableHead className="modern-table th">التكلفة/الوحدة</TableHead>
                    <TableHead className="modern-table th">القيمة الإجمالية</TableHead>
                    <TableHead className="modern-table th">المورد</TableHead>
                    <TableHead className="modern-table th">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {materials.map((material) => {
                    const stockStatus = getStockStatus(material.current_stock, material.min_stock, material.max_stock)
                    const totalValue = material.current_stock * material.cost_per_unit

                    return (
                      <TableRow key={material.id} className="modern-table tr">
                        <TableCell className="modern-table td">
                          <div className="font-semibold text-slate-900">{material.name}</div>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <Badge className="bg-indigo-100 text-indigo-700 border-indigo-200 border rounded-full px-3 py-1">
                            {material.category}
                          </Badge>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <div className="font-medium">
                            {material.current_stock} {material.unit}
                          </div>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <Badge className={`${stockStatus.color} border rounded-full px-3 py-1`}>
                            {stockStatus.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <div className="font-medium">{material.cost_per_unit.toFixed(2)} د.ل</div>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <div className="font-semibold text-emerald-600">{totalValue.toFixed(2)} د.ل</div>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <div className="text-slate-700">{material.supplier}</div>
                        </TableCell>
                        <TableCell className="modern-table td">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteMutation.mutate(material.id)}
                            disabled={deleteMutation.loading}
                            className="rounded-xl border-red-200 text-red-600 hover:bg-red-50"
                          >
                            {deleteMutation.loading ? <Loader2 className="h-3 w-3 animate-spin" /> : "حذف"}
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
