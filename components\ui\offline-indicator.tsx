"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Wifi, WifiOff } from "lucide-react"

export function OfflineIndicator() {
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // For Tauri apps, we're always "offline" in terms of internet dependency
    // but we can show the local database status
    setIsOnline(false) // Always show offline mode for desktop app
  }, [])

  return (
    <Badge
      variant="outline"
      className={`${
        isOnline ? "border-emerald-200 text-emerald-700 bg-emerald-50" : "border-blue-200 text-blue-700 bg-blue-50"
      } flex items-center space-x-1 space-x-reverse`}
    >
      {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
      <span className="text-xs">{isOnline ? "متصل" : "وضع محلي"}</span>
    </Badge>
  )
}
