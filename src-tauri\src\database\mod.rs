use sqlx::{SqlitePool, migrate::MigrateDatabase, Sqlite};
use std::path::PathBuf;
use tauri::api::path::app_data_dir;
use crate::error::Result;

pub struct Database {
    pub pool: SqlitePool,
}

impl Database {
    pub async fn new() -> Result<Self> {
        let app_data_dir = app_data_dir(&tauri::Config::default())
            .ok_or_else(|| crate::error::Error::Database("Could not find app data directory".to_string()))?;
        
        let db_dir = app_data_dir.join("furniture_factory");
        std::fs::create_dir_all(&db_dir)?;
        
        let db_path = db_dir.join("furniture_factory.db");
        let db_url = format!("sqlite:{}", db_path.display());

        // Create database if it doesn't exist
        if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {
            Sqlite::create_database(&db_url).await?;
        }

        let pool = SqlitePool::connect(&db_url).await?;
        
        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;

        Ok(Database { pool })
    }

    pub async fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }
}
