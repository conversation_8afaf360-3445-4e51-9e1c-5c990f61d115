use crate::error::Result;

pub struct Database {
    pub pool: SqlitePool,
}

impl Database {
    pub async fn new() -> Result<Self> {
        // Use a simple path for the database in the current directory for now
        let db_dir = std::env::current_dir()
            .map_err(|e| crate::error::Error::Database(format!("Could not get current directory: {}", e)))?
            .join("data");

        std::fs::create_dir_all(&db_dir)?;

        let db_path = db_dir.join("furniture_factory.db");
        let db_url = format!("sqlite:{}", db_path.display());

        // Create database if it doesn't exist
        if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {
            Sqlite::create_database(&db_url).await?;
        }

        let pool = SqlitePool::connect(&db_url).await?;

        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;

        Ok(Database { pool })
    }

    pub async fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }

    // Employee methods
    pub async fn get_employees(&self) -> Result<Vec<crate::models::Employee>> {
        let employees = sqlx::query_as::<_, crate::models::Employee>(
            "SELECT id, name, position, department, hourly_rate, hours_worked, overtime, status, vacation_days, allowances, deductions, social_insurance, discount, discount_reason, join_date, created_at, updated_at FROM employees ORDER BY name"
        )
        .fetch_all(&self.pool)
        .await?;
        Ok(employees)
    }

    pub async fn create_employee(&self, employee: crate::models::CreateEmployee) -> Result<crate::models::Employee> {
        let result = sqlx::query(
            r#"
            INSERT INTO employees (name, position, department, hourly_rate, allowances, deductions)
            VALUES (?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&employee.name)
        .bind(&employee.position)
        .bind(&employee.department)
        .bind(employee.hourly_rate)
        .bind(employee.allowances)
        .bind(employee.deductions)
        .execute(&self.pool)
        .await?;

        let employee = sqlx::query_as::<_, crate::models::Employee>(
            "SELECT id, name, position, department, hourly_rate, hours_worked, overtime, status, vacation_days, allowances, deductions, social_insurance, discount, discount_reason, join_date, created_at, updated_at FROM employees WHERE id = ?"
        )
        .bind(result.last_insert_rowid())
        .fetch_one(&self.pool)
        .await?;

        Ok(employee)
    }

    pub async fn update_employee(&self, id: i64, employee: crate::models::CreateEmployee) -> Result<crate::models::Employee> {
        sqlx::query(
            r#"
            UPDATE employees
            SET name = ?, position = ?, department = ?, hourly_rate = ?, allowances = ?, deductions = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            "#
        )
        .bind(&employee.name)
        .bind(&employee.position)
        .bind(&employee.department)
        .bind(employee.hourly_rate)
        .bind(employee.allowances)
        .bind(employee.deductions)
        .bind(id)
        .execute(&self.pool)
        .await?;

        let employee = sqlx::query_as::<_, crate::models::Employee>(
            "SELECT id, name, position, department, hourly_rate, hours_worked, overtime, status, vacation_days, allowances, deductions, social_insurance, discount, discount_reason, join_date, created_at, updated_at FROM employees WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await?;

        Ok(employee)
    }

    pub async fn delete_employee(&self, id: i64) -> Result<bool> {
        let result = sqlx::query("DELETE FROM employees WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;
        Ok(result.rows_affected() > 0)
    }

    pub async fn calculate_payroll(&self, employee_id: i64, month: String) -> Result<f64> {
        let employee = sqlx::query_as::<_, crate::models::Employee>(
            "SELECT id, name, position, department, hourly_rate, hours_worked, overtime, status, vacation_days, allowances, deductions, social_insurance, discount, discount_reason, join_date, created_at, updated_at FROM employees WHERE id = ?"
        )
        .bind(employee_id)
        .fetch_one(&self.pool)
        .await?;

        // Calculate based on hourly rate and hours worked
        let base_pay = employee.hourly_rate * employee.hours_worked;
        let overtime_pay = employee.hourly_rate * 1.5 * employee.overtime;
        let total = base_pay + overtime_pay + employee.allowances - employee.deductions - employee.social_insurance;
        Ok(total)
    }

    // Project methods
    pub async fn get_projects(&self) -> Result<Vec<crate::models::Project>> {
        let projects = sqlx::query_as::<_, crate::models::Project>(
            "SELECT id, name, customer_id, assigned_employee_id, furniture_type, square_meters, status, stage, total_amount, first_payment, final_payment, materials_cost, labor_cost, designer_fee, factory_overhead, profit_margin, start_date, expected_completion, actual_completion, created_at, updated_at FROM projects ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;
        Ok(projects)
    }

    pub async fn create_project(&self, project: crate::models::CreateProject) -> Result<crate::models::Project> {
        let result = sqlx::query!(
            r#"
            INSERT INTO projects (name, customer_id, assigned_employee_id, furniture_type, square_meters, designer_fee, factory_overhead, profit_margin, expected_completion)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            project.name,
            project.customer_id,
            project.assigned_employee_id,
            project.furniture_type,
            project.square_meters,
            project.designer_fee,
            project.factory_overhead,
            project.profit_margin,
            project.expected_completion
        )
        .execute(&self.pool)
        .await?;

        let project = sqlx::query_as::<_, crate::models::Project>(
            "SELECT id, name, customer_id, assigned_employee_id, furniture_type, square_meters, status, stage, total_amount, first_payment, final_payment, materials_cost, labor_cost, designer_fee, factory_overhead, profit_margin, start_date, expected_completion, actual_completion, created_at, updated_at FROM projects WHERE id = ?"
        )
        .bind(result.last_insert_rowid())
        .fetch_one(&self.pool)
        .await?;

        Ok(project)
    }

    pub async fn update_project(&self, id: i64, project: crate::models::CreateProject) -> Result<crate::models::Project> {
        sqlx::query!(
            r#"
            UPDATE projects
            SET name = ?, customer_id = ?, assigned_employee_id = ?, furniture_type = ?, square_meters = ?, designer_fee = ?, factory_overhead = ?, profit_margin = ?, expected_completion = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            "#,
            project.name,
            project.customer_id,
            project.assigned_employee_id,
            project.furniture_type,
            project.square_meters,
            project.designer_fee,
            project.factory_overhead,
            project.profit_margin,
            project.expected_completion,
            id
        )
        .execute(&self.pool)
        .await?;

        let project = sqlx::query_as::<_, crate::models::Project>(
            "SELECT id, name, customer_id, assigned_employee_id, furniture_type, square_meters, status, stage, total_amount, first_payment, final_payment, materials_cost, labor_cost, designer_fee, factory_overhead, profit_margin, start_date, expected_completion, actual_completion, created_at, updated_at FROM projects WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await?;

        Ok(project)
    }

    pub async fn delete_project(&self, id: i64) -> Result<bool> {
        let result = sqlx::query!("DELETE FROM projects WHERE id = ?", id)
            .execute(&self.pool)
            .await?;
        Ok(result.rows_affected() > 0)
    }

    pub async fn calculate_project_cost(&self, project_id: i64) -> Result<f64> {
        let project = sqlx::query_as::<_, crate::models::Project>(
            "SELECT id, name, customer_id, assigned_employee_id, furniture_type, square_meters, status, stage, total_amount, first_payment, final_payment, materials_cost, labor_cost, designer_fee, factory_overhead, profit_margin, start_date, expected_completion, actual_completion, created_at, updated_at FROM projects WHERE id = ?"
        )
        .bind(project_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(project.total_amount)
    }

    // Payment methods
    pub async fn get_payments(&self) -> Result<Vec<crate::models::Payment>> {
        let payments = sqlx::query_as::<_, crate::models::Payment>(
            "SELECT id, project_id, payment_type, amount, status, invoice_number, payment_date, created_at, updated_at FROM payments ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;
        Ok(payments)
    }

    pub async fn create_payment(&self, payment: crate::models::CreatePayment) -> Result<crate::models::Payment> {
        // For now, create a simple payment with basic fields
        let invoice_number = format!("INV-{}", chrono::Utc::now().timestamp());

        let result = sqlx::query!(
            r#"
            INSERT INTO payments (project_id, payment_type, amount, status, invoice_number)
            VALUES (?, ?, ?, ?, ?)
            "#,
            payment.project_id,
            payment.payment_type,
            payment.amount,
            payment.status,
            invoice_number
        )
        .execute(&self.pool)
        .await?;

        let payment = sqlx::query_as::<_, crate::models::Payment>(
            "SELECT id, project_id, payment_type, amount, status, invoice_number, payment_date, created_at, updated_at FROM payments WHERE id = ?"
        )
        .bind(result.last_insert_rowid())
        .fetch_one(&self.pool)
        .await?;

        Ok(payment)
    }

    pub async fn update_payment_status(&self, id: i64, status: String) -> Result<crate::models::Payment> {
        let payment_date = if status == "paid" { Some(chrono::Utc::now()) } else { None };

        sqlx::query!(
            "UPDATE payments SET status = ?, payment_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            status,
            payment_date,
            id
        )
        .execute(&self.pool)
        .await?;

        let payment = sqlx::query_as::<_, crate::models::Payment>(
            "SELECT id, project_id, payment_type, amount, status, invoice_number, payment_date, created_at, updated_at FROM payments WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await?;

        Ok(payment)
    }

    pub async fn generate_invoice(&self, payment_id: i64) -> Result<String> {
        let payment = sqlx::query_as::<_, crate::models::Payment>(
            "SELECT id, project_id, payment_type, amount, status, invoice_number, payment_date, created_at, updated_at FROM payments WHERE id = ?"
        )
        .bind(payment_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(format!("Invoice {}: Payment ID: {}, Amount: {}", payment.invoice_number, payment.id, payment.amount))
    }

    // Treasury methods
    pub async fn get_transactions(&self) -> Result<Vec<crate::models::Transaction>> {
        let transactions = sqlx::query_as::<_, crate::models::Transaction>(
            "SELECT id, description, transaction_type, category, amount, status, reference, project_id, transaction_date, created_at FROM transactions ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;
        Ok(transactions)
    }

    pub async fn create_transaction(&self, transaction: crate::models::CreateTransaction) -> Result<crate::models::Transaction> {
        let result = sqlx::query!(
            r#"
            INSERT INTO transactions (description, transaction_type, category, amount, status, reference)
            VALUES (?, ?, ?, ?, 'completed', ?)
            "#,
            transaction.description,
            transaction.transaction_type,
            transaction.category,
            transaction.amount,
            transaction.reference
        )
        .execute(&self.pool)
        .await?;

        let transaction = sqlx::query_as::<_, crate::models::Transaction>(
            "SELECT id, description, transaction_type, category, amount, status, reference, project_id, transaction_date, created_at FROM transactions WHERE id = ?"
        )
        .bind(result.last_insert_rowid())
        .fetch_one(&self.pool)
        .await?;

        Ok(transaction)
    }

    pub async fn get_account_balances(&self) -> Result<Vec<crate::models::AccountBalance>> {
        // Simple implementation - calculate balances from transactions
        let categories = vec!["main", "savings", "cash"];
        let mut balances = Vec::new();

        for category in categories {
            let balance: f64 = sqlx::query_scalar!(
                "SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) FROM transactions WHERE category = ?",
                category
            )
            .fetch_one(&self.pool)
            .await?
            .unwrap_or(0.0);

            balances.push(crate::models::AccountBalance {
                account: category.to_string(),
                balance,
                last_updated: chrono::Utc::now(),
            });
        }

        Ok(balances)
    }

    // Reports methods
    pub async fn generate_production_report(&self, start_date: String, end_date: String) -> Result<String> {
        let count: i64 = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM projects WHERE created_at BETWEEN ? AND ?",
            start_date,
            end_date
        )
        .fetch_one(&self.pool)
        .await?
        .unwrap_or(0);

        Ok(format!("Production Report: {} projects between {} and {}", count, start_date, end_date))
    }

    pub async fn generate_financial_report(&self, start_date: String, end_date: String) -> Result<String> {
        let total: f64 = sqlx::query_scalar!(
            "SELECT COALESCE(SUM(amount), 0) FROM payments WHERE created_at BETWEEN ? AND ?",
            start_date,
            end_date
        )
        .fetch_one(&self.pool)
        .await?
        .unwrap_or(0.0);

        Ok(format!("Financial Report: {} total payments between {} and {}", total, start_date, end_date))
    }

    pub async fn export_report(&self, report_type: String, format: String) -> Result<String> {
        Ok(format!("Exported {} report in {} format", report_type, format))
    }

    // Backup methods
    pub async fn create_backup(&self, backup_name: String) -> Result<String> {
        let backup_path = format!("backups/{}.db", backup_name);
        Ok(backup_path)
    }

    pub async fn restore_backup(&self, backup_path: String) -> Result<bool> {
        // Simple implementation - just return success
        Ok(true)
    }

    pub async fn list_backups(&self) -> Result<Vec<String>> {
        Ok(vec!["backup1.db".to_string(), "backup2.db".to_string()])
    }
}
