use crate::database::Database;
use crate::models::{Project, CreateProject};
use crate::error::Result;
use tauri::State;

#[tauri::command]
pub async fn get_projects(db: State<'_, Database>) -> Result<Vec<Project>> {
    db.get_projects().await
}

#[tauri::command]
pub async fn create_project(db: State<'_, Database>, project: CreateProject) -> Result<Project> {
    db.create_project(project).await
}

#[tauri::command]
pub async fn update_project(db: State<'_, Database>, id: i64, project: CreateProject) -> Result<Project> {
    db.update_project(id, project).await
}

#[tauri::command]
pub async fn delete_project(db: State<'_, Database>, id: i64) -> Result<bool> {
    db.delete_project(id).await
}

#[tauri::command]
pub async fn calculate_project_cost(db: State<'_, Database>, project_id: i64) -> Result<f64> {
    db.calculate_project_cost(project_id).await
}
