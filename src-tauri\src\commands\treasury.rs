use crate::database::Database;
use crate::models::{Transaction, CreateTransaction, AccountBalance};
use crate::error::Result;
use tauri::State;

#[tauri::command]
pub async fn get_transactions(db: State<'_, Database>) -> Result<Vec<Transaction>> {
    db.get_transactions().await
}

#[tauri::command]
pub async fn create_transaction(db: State<'_, Database>, transaction: CreateTransaction) -> Result<Transaction> {
    db.create_transaction(transaction).await
}

#[tauri::command]
pub async fn get_account_balances(db: State<'_, Database>) -> Result<Vec<AccountBalance>> {
    db.get_account_balances().await
}
