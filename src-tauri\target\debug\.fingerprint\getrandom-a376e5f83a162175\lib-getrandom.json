{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2225463790103693989, "path": 10613793752382407370, "deps": [[5170503507811329045, "build_script_build", false, 15873669376797933763], [10411997081178400487, "cfg_if", false, 13255781465023754910]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-a376e5f83a162175\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}