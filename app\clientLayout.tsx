"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Navigation } from "@/components/navigation"
import "./globals.css"

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Check if user is authenticated
    const user = localStorage.getItem("user")
    if (user) {
      setIsAuthenticated(true)
    } else if (pathname !== "/login") {
      router.push("/login")
    }
    setIsLoading(false)
  }, [pathname, router])

  if (isLoading) {
    return (
      <html lang="ar" dir="rtl">
        <body>
          <div className="min-h-screen bg-white flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-2xl">H</span>
              </div>
              <p className="text-gray-600">جاري التحميل...</p>
            </div>
          </div>
        </body>
      </html>
    )
  }

  if (!isAuthenticated && pathname !== "/login") {
    return null
  }

  return (
    <html lang="ar" dir="rtl">
      <head>
        <title>مجموعة H - نظام إدارة مصنع الأثاث</title>
        <meta name="description" content="نظام شامل لإدارة الإنتاج والأعمال لتصنيع الأثاث بالدينار الليبي" />
      </head>
      <body>
        {pathname !== "/login" && <Navigation />}
        {children}
      </body>
    </html>
  )
}
