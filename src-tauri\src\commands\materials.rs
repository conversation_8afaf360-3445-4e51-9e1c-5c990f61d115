use tauri::State;
use crate::{database::Database, models::*, error::Result};
use serde_json::Value;

#[tauri::command]
pub async fn get_materials(db: State<'_, Database>) -> Result<Vec<Material>> {
    let materials = sqlx::query_as::<_, Material>(
        "SELECT * FROM materials ORDER BY name"
    )
    .fetch_all(&db.pool)
    .await?;
    
    Ok(materials)
}

#[tauri::command]
pub async fn create_material(
    db: State<'_, Database>,
    material: CreateMaterial,
) -> Result<Material> {
    let now = chrono::Utc::now();
    
    let result = sqlx::query_as::<_, Material>(
        r#"
        INSERT INTO materials (
            name, category, current_stock, min_stock, max_stock,
            unit, cost_per_unit, supplier, last_restocked, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        RETURNING *
        "#
    )
    .bind(&material.name)
    .bind(&material.category)
    .bind(material.current_stock)
    .bind(material.min_stock)
    .bind(material.max_stock)
    .bind(&material.unit)
    .bind(material.cost_per_unit)
    .bind(&material.supplier)
    .bind(now)
    .bind(now)
    .bind(now)
    .fetch_one(&db.pool)
    .await?;

    Ok(result)
}

#[tauri::command]
pub async fn update_material(
    db: State<'_, Database>,
    id: i64,
    material: CreateMaterial,
) -> Result<Material> {
    let now = chrono::Utc::now();
    
    let result = sqlx::query_as::<_, Material>(
        r#"
        UPDATE materials SET
            name = ?, category = ?, current_stock = ?, min_stock = ?, max_stock = ?,
            unit = ?, cost_per_unit = ?, supplier = ?, updated_at = ?
        WHERE id = ?
        RETURNING *
        "#
    )
    .bind(&material.name)
    .bind(&material.category)
    .bind(material.current_stock)
    .bind(material.min_stock)
    .bind(material.max_stock)
    .bind(&material.unit)
    .bind(material.cost_per_unit)
    .bind(&material.supplier)
    .bind(now)
    .bind(id)
    .fetch_one(&db.pool)
    .await?;

    Ok(result)
}

#[tauri::command]
pub async fn delete_material(db: State<'_, Database>, id: i64) -> Result<bool> {
    let result = sqlx::query("DELETE FROM materials WHERE id = ?")
        .bind(id)
        .execute(&db.pool)
        .await?;

    Ok(result.rows_affected() > 0)
}

#[tauri::command]
pub async fn import_materials(
    db: State<'_, Database>,
    materials: Vec<CreateMaterial>,
) -> Result<Vec<Material>> {
    let mut imported = Vec::new();
    let now = chrono::Utc::now();

    for material in materials {
        let result = sqlx::query_as::<_, Material>(
            r#"
            INSERT INTO materials (
                name, category, current_stock, min_stock, max_stock,
                unit, cost_per_unit, supplier, last_restocked, created_at, updated_at
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#
        )
        .bind(&material.name)
        .bind(&material.category)
        .bind(material.current_stock)
        .bind(material.min_stock)
        .bind(material.max_stock)
        .bind(&material.unit)
        .bind(material.cost_per_unit)
        .bind(&material.supplier)
        .bind(now)
        .bind(now)
        .bind(now)
        .fetch_one(&db.pool)
        .await?;

        imported.push(result);
    }

    Ok(imported)
}

#[tauri::command]
pub async fn export_materials(db: State<'_, Database>) -> Result<String> {
    let materials = get_materials(db).await?;
    
    // Convert to CSV format
    let mut csv = String::from("اسم المادة,الفئة,المخزون الحالي,الحد الأدنى,الحد الأقصى,الوحدة,التكلفة لكل وحدة,المورد,آخر تخزين\n");
    
    for material in materials {
        csv.push_str(&format!(
            "{},{},{},{},{},{},{},{},{}\n",
            material.name,
            material.category,
            material.current_stock,
            material.min_stock,
            material.max_stock,
            material.unit,
            material.cost_per_unit,
            material.supplier,
            material.last_restocked.format("%Y-%m-%d")
        ));
    }

    Ok(csv)
}
