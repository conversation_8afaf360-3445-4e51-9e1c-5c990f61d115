#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod database;
mod models;
mod commands;
mod error;

use database::Database;
use tauri::Manager;

#[tokio::main]
async fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .setup(|app| {
            let app_handle = app.handle();

            // Initialize database
            tauri::async_runtime::spawn(async move {
                let db = Database::new().await.expect("Failed to initialize database");
                app_handle.manage(db);
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Material commands
            commands::materials::get_materials,
            commands::materials::create_material,
            commands::materials::update_material,
            commands::materials::delete_material,

            // Customer commands
            commands::customers::get_customers,
            commands::customers::create_customer,
            commands::customers::update_customer,
            commands::customers::delete_customer,

            // Dashboard commands
            commands::dashboard::get_dashboard_stats,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
