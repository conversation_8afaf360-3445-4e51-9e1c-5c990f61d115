#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod database;
mod models;
mod commands;
mod error;

use database::Database;
use tauri::{Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, CustomMenuItem};

#[tokio::main]
async fn main() {
    // Create system tray
    let quit = CustomMenuItem::new("quit".to_string(), "إنهاء");
    let show = CustomMenuItem::new("show".to_string(), "إظهار");
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_native_item(tauri::SystemTrayMenuItem::Separator)
        .add_item(quit);
    let system_tray = SystemTray::new().with_menu(tray_menu);

    tauri::Builder::default()
        .system_tray(system_tray)
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick {
                position: _,
                size: _,
                ..
            } => {
                let window = app.get_window("main").unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            SystemTrayEvent::MenuItemClick { id, .. } => match id.as_str() {
                "quit" => {
                    std::process::exit(0);
                }
                "show" => {
                    let window = app.get_window("main").unwrap();
                    window.show().unwrap();
                    window.set_focus().unwrap();
                }
                _ => {}
            },
            _ => {}
        })
        .setup(|app| {
            let app_handle = app.handle();
            
            // Initialize database
            tauri::async_runtime::spawn(async move {
                let db = Database::new().await.expect("Failed to initialize database");
                app_handle.manage(db);
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Material commands
            commands::materials::get_materials,
            commands::materials::create_material,
            commands::materials::update_material,
            commands::materials::delete_material,
            commands::materials::import_materials,
            commands::materials::export_materials,
            
            // Customer commands
            commands::customers::get_customers,
            commands::customers::create_customer,
            commands::customers::update_customer,
            commands::customers::delete_customer,
            commands::customers::get_customer_by_id,
            
            // Employee commands
            commands::employees::get_employees,
            commands::employees::create_employee,
            commands::employees::update_employee,
            commands::employees::delete_employee,
            commands::employees::calculate_payroll,
            
            // Project commands
            commands::projects::get_projects,
            commands::projects::create_project,
            commands::projects::update_project,
            commands::projects::delete_project,
            commands::projects::calculate_project_cost,
            
            // Payment commands
            commands::payments::get_payments,
            commands::payments::create_payment,
            commands::payments::update_payment_status,
            commands::payments::generate_invoice,
            
            // Treasury commands
            commands::treasury::get_transactions,
            commands::treasury::create_transaction,
            commands::treasury::get_account_balances,
            
            // Dashboard commands
            commands::dashboard::get_dashboard_stats,
            commands::dashboard::get_recent_activities,
            
            // Reports commands
            commands::reports::generate_production_report,
            commands::reports::generate_financial_report,
            commands::reports::export_report,
            
            // Backup commands
            commands::backup::create_backup,
            commands::backup::restore_backup,
            commands::backup::list_backups,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
