#!/bin/bash

echo "========================================"
echo "  إصلاح تبعيات المشروع وبناء التطبيق"
echo "========================================"
echo

echo "الخطوة 1: تنظيف الملفات القديمة..."
rm -rf node_modules package-lock.json out
echo "تم تنظيف الملفات القديمة."
echo

echo "الخطوة 2: تثبيت التبعيات مع تجاهل التعارضات..."
npm install --legacy-peer-deps
if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت التبعيات!"
    exit 1
fi
echo "تم تثبيت التبعيات بنجاح."
echo

echo "الخطوة 3: بناء تطبيق Next.js..."
npm run build
if [ $? -ne 0 ]; then
    echo "خطأ في بناء Next.js!"
    exit 1
fi
echo "تم بناء Next.js بنجاح."
echo

echo "الخطوة 4: التحقق من وجود Rust..."
rustc --version
if [ $? -ne 0 ]; then
    echo "Rust غير مثبت! يرجى تثبيت Rust من https://rustup.rs/"
    exit 1
fi
echo "Rust مثبت ومتاح."
echo

echo "الخطوة 5: بناء تطبيق Tauri..."
npm run tauri:build
if [ $? -ne 0 ]; then
    echo "خطأ في بناء Tauri!"
    echo "جرب تشغيل: npm run tauri:build:debug"
    exit 1
fi
echo

echo "========================================"
echo "   تم بناء التطبيق بنجاح! 🎉"
echo "========================================"
echo
echo "ملفات التطبيق موجودة في:"
echo "- macOS: src-tauri/target/release/bundle/dmg/"
echo "- Linux: src-tauri/target/release/bundle/deb/"
echo "- Executable: src-tauri/target/release/"
echo
echo "لتشغيل التطبيق في وضع التطوير:"
echo "npm run tauri:dev"
echo
