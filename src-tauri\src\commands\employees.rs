use crate::database::Database;
use crate::models::{Em<PERSON>loyee, CreateEmployee};
use crate::error::Result;
use tauri::State;

#[tauri::command]
pub async fn get_employees(db: State<'_, Database>) -> Result<Vec<Employee>> {
    db.get_employees().await
}

#[tauri::command]
pub async fn create_employee(db: State<'_, Database>, employee: CreateEmployee) -> Result<Employee> {
    db.create_employee(employee).await
}

#[tauri::command]
pub async fn update_employee(db: State<'_, Database>, id: i64, employee: CreateEmployee) -> Result<Employee> {
    db.update_employee(id, employee).await
}

#[tauri::command]
pub async fn delete_employee(db: State<'_, Database>, id: i64) -> Result<bool> {
    db.delete_employee(id).await
}

#[tauri::command]
pub async fn calculate_payroll(db: State<'_, Database>, employee_id: i64, month: String) -> Result<f64> {
    db.calculate_payroll(employee_id, month).await
}
