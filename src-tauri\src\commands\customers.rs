use tauri::State;
use crate::{database::Database, models::*, error::Result};

#[tauri::command]
pub async fn get_customers(db: State<'_, Database>) -> Result<Vec<Customer>> {
    let customers = sqlx::query_as::<_, Customer>(
        "SELECT * FROM customers ORDER BY name"
    )
    .fetch_all(&db.pool)
    .await?;
    
    Ok(customers)
}

#[tauri::command]
pub async fn get_customer_by_id(db: State<'_, Database>, id: i64) -> Result<Option<Customer>> {
    let customer = sqlx::query_as::<_, Customer>(
        "SELECT * FROM customers WHERE id = ?"
    )
    .bind(id)
    .fetch_optional(&db.pool)
    .await?;
    
    Ok(customer)
}

#[tauri::command]
pub async fn create_customer(
    db: State<'_, Database>,
    customer: CreateCustomer,
) -> Result<Customer> {
    let now = chrono::Utc::now();
    
    let result = sqlx::query_as::<_, Customer>(
        r#"
        INSERT INTO customers (
            name, email, phone, address, customer_type, status,
            total_projects, total_spent, discount, registration_date,
            last_interaction, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, 'نشط', 0, 0.0, 0.0, ?, ?, ?, ?)
        RETURNING *
        "#
    )
    .bind(&customer.name)
    .bind(&customer.email)
    .bind(&customer.phone)
    .bind(&customer.address)
    .bind(&customer.customer_type)
    .bind(now)
    .bind(now)
    .bind(now)
    .bind(now)
    .fetch_one(&db.pool)
    .await?;

    Ok(result)
}

#[tauri::command]
pub async fn update_customer(
    db: State<'_, Database>,
    id: i64,
    customer: CreateCustomer,
) -> Result<Customer> {
    let now = chrono::Utc::now();
    
    let result = sqlx::query_as::<_, Customer>(
        r#"
        UPDATE customers SET
            name = ?, email = ?, phone = ?, address = ?, customer_type = ?,
            updated_at = ?
        WHERE id = ?
        RETURNING *
        "#
    )
    .bind(&customer.name)
    .bind(&customer.email)
    .bind(&customer.phone)
    .bind(&customer.address)
    .bind(&customer.customer_type)
    .bind(now)
    .bind(id)
    .fetch_one(&db.pool)
    .await?;

    Ok(result)
}

#[tauri::command]
pub async fn delete_customer(db: State<'_, Database>, id: i64) -> Result<bool> {
    let result = sqlx::query("DELETE FROM customers WHERE id = ?")
        .bind(id)
        .execute(&db.pool)
        .await?;

    Ok(result.rows_affected() > 0)
}
