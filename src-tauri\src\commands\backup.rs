use crate::database::Database;
use crate::error::Result;
use tauri::State;

#[tauri::command]
pub async fn create_backup(db: State<'_, Database>, backup_name: String) -> Result<String> {
    db.create_backup(backup_name).await
}

#[tauri::command]
pub async fn restore_backup(db: State<'_, Database>, backup_path: String) -> Result<bool> {
    db.restore_backup(backup_path).await
}

#[tauri::command]
pub async fn list_backups(db: State<'_, Database>) -> Result<Vec<String>> {
    db.list_backups().await
}
