"use client"

import { useState, useEffect, useCallback } from "react"

export function useTauriQuery<T>(queryFn: () => Promise<T>, deps: any[] = []) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await queryFn()
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : "حدث خطأ غير متوقع")
    } finally {
      setLoading(false)
    }
  }, deps)

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  }
}

export function useTauriMutation<T, P>(
  mutationFn: (params: P) => Promise<T>,
  onSuccess?: (data: T) => void,
  onError?: (error: string) => void,
) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mutate = useCallback(
    async (params: P) => {
      setLoading(true)
      setError(null)

      try {
        const result = await mutationFn(params)
        onSuccess?.(result)
        return result
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "حدث خطأ غير متوقع"
        setError(errorMessage)
        onError?.(errorMessage)
        throw err
      } finally {
        setLoading(false)
      }
    },
    [mutationFn, onSuccess, onError],
  )

  return {
    mutate,
    loading,
    error,
  }
}
