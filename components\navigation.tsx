"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Home, Calculator, Package, Users, DollarSign, FileText, CreditCard, UserCheck, LogOut } from "lucide-react"

const navigation = [
  { name: "لوحة التحكم", href: "/", icon: Home },
  { name: "حاسبة الإنتاج", href: "/production", icon: Calculator },
  { name: "إدارة المواد", href: "/materials", icon: Package },
  { name: "إدارة الرواتب", href: "/payroll", icon: Users },
  { name: "الخزينة", href: "/treasury", icon: DollarSign },
  { name: "نظام المدفوعات", href: "/payments", icon: CreditCard },
  { name: "إدارة العملاء", href: "/crm", icon: User<PERSON>he<PERSON> },
  { name: "التقارير", href: "/reports", icon: FileText },
]

export function Navigation() {
  const pathname = usePathname()

  const handleLogout = () => {
    localStorage.removeItem("user")
    window.location.href = "/login"
  }

  return (
    <nav className="modern-nav sticky top-0 z-50 px-6 py-4">
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 space-x-reverse">
            <Link href="/" className="flex items-center space-x-3 space-x-reverse group">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                <span className="text-white font-bold text-xl">H</span>
              </div>
              <div>
                <span className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  مجموعة H
                </span>
                <p className="text-xs text-slate-500">نظام إدارة المصنع</p>
              </div>
            </Link>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <div className="flex space-x-1 space-x-reverse bg-slate-100 rounded-2xl p-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href || (item.href === "/crm" && pathname.startsWith("/crm"))
                return (
                  <Link key={item.name} href={item.href}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "flex items-center space-x-2 space-x-reverse rounded-xl px-4 py-2 transition-all duration-200",
                        isActive
                          ? "bg-white text-indigo-600 shadow-sm font-medium"
                          : "text-slate-600 hover:text-slate-900 hover:bg-white/50",
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      <span className="hidden lg:inline text-sm">{item.name}</span>
                    </Button>
                  </Link>
                )
              })}
            </div>

            <Button
              onClick={handleLogout}
              variant="ghost"
              size="sm"
              className="text-slate-500 hover:text-red-600 hover:bg-red-50 rounded-xl px-3 py-2 transition-all duration-200"
            >
              <LogOut className="h-4 w-4" />
              <span className="hidden md:inline mr-2">خروج</span>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  )
}
