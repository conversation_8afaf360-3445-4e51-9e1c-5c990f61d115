"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calculator, ArrowLeft, Save, FileText, CreditCard, Plus, Trash2, Users } from "lucide-react"
import Link from "next/link"

export default function ProductionCalculator() {
  const [formData, setFormData] = useState({
    projectName: "",
    furnitureType: "",
    squareMeters: "",
    designerFee: "",
    factoryOverhead: "",
    profitMargin: "",
    assignedEmployee: "",
    customerId: "",
  })

  const [materials, setMaterials] = useState([
    {
      id: 1,
      name: "",
      type: "",
      squareMetersNeeded: "",
      costPerSquareMeter: "",
    },
  ])

  const [accessories, setAccessories] = useState([
    {
      id: 1,
      name: "",
      type: "",
      quantity: "",
      costPerUnit: "",
    },
  ])

  const [calculation, setCalculation] = useState(null)

  // Mock data for employees and customers
  const employees = [
    { id: 1, name: "أحمد محمد السالم", department: "الإنتاج", specialty: "نجارة" },
    { id: 2, name: "مريم أحمد الغامدي", department: "التصميم", specialty: "تصميم أثاث" },
    { id: 3, name: "خالد عبدالله الزهراني", department: "الإنتاج", specialty: "تجميع" },
    { id: 4, name: "فاطمة سعد القحطاني", department: "مراقبة الجودة", specialty: "فحص جودة" },
  ]

  const customers = [
    { id: 1, name: "عائلة الأحمد" },
    { id: 2, name: "شركة التقنية المتقدمة" },
    { id: 3, name: "منزل العائلة" },
  ]

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleMaterialChange = (id, field, value) => {
    setMaterials((prev) => prev.map((material) => (material.id === id ? { ...material, [field]: value } : material)))
  }

  const handleAccessoryChange = (id, field, value) => {
    setAccessories((prev) =>
      prev.map((accessory) => (accessory.id === id ? { ...accessory, [field]: value } : accessory)),
    )
  }

  const addMaterial = () => {
    const newId = Math.max(...materials.map((m) => m.id)) + 1
    setMaterials((prev) => [
      ...prev,
      {
        id: newId,
        name: "",
        type: "",
        squareMetersNeeded: "",
        costPerSquareMeter: "",
      },
    ])
  }

  const removeMaterial = (id) => {
    if (materials.length > 1) {
      setMaterials((prev) => prev.filter((material) => material.id !== id))
    }
  }

  const addAccessory = () => {
    const newId = Math.max(...accessories.map((a) => a.id)) + 1
    setAccessories((prev) => [
      ...prev,
      {
        id: newId,
        name: "",
        type: "",
        quantity: "",
        costPerUnit: "",
      },
    ])
  }

  const removeAccessory = (id) => {
    if (accessories.length > 1) {
      setAccessories((prev) => prev.filter((accessory) => accessory.id !== id))
    }
  }

  const calculateCosts = () => {
    const designerFee = Number.parseFloat(formData.designerFee) || 0
    const factoryOverhead = Number.parseFloat(formData.factoryOverhead) || 0
    const profitMargin = Number.parseFloat(formData.profitMargin) || 0
    const squareMeters = Number.parseFloat(formData.squareMeters) || 0

    // Calculate materials cost based on square meters
    const materialsCost = materials.reduce((total, material) => {
      const sqmNeeded = Number.parseFloat(material.squareMetersNeeded) || 0
      const costPerSqm = Number.parseFloat(material.costPerSquareMeter) || 0
      return total + sqmNeeded * costPerSqm
    }, 0)

    // Calculate accessories cost
    const accessoriesCost = accessories.reduce((total, accessory) => {
      const quantity = Number.parseFloat(accessory.quantity) || 0
      const costPerUnit = Number.parseFloat(accessory.costPerUnit) || 0
      return total + quantity * costPerUnit
    }, 0)

    // Calculate labor cost based on square meters and complexity
    const baseHoursPerSqm = 2 // Base hours per square meter
    const complexityMultiplier = 1 + materials.length * 0.1 + accessories.length * 0.05
    const estimatedHours = squareMeters * baseHoursPerSqm * complexityMultiplier
    const laborRate = 6.25 // LYD per hour (converted from 25 SAR)
    const laborCost = estimatedHours * laborRate

    const totalDirectCost = materialsCost + accessoriesCost + laborCost + designerFee
    const totalWithOverhead = totalDirectCost + (totalDirectCost * factoryOverhead) / 100
    const finalPrice = totalWithOverhead + (totalWithOverhead * profitMargin) / 100

    // Calculate payment breakdown
    const firstPayment = finalPrice * 0.7 // 70%
    const finalPayment = finalPrice * 0.3 // 30%

    setCalculation({
      materialsCost: materialsCost.toFixed(2),
      accessoriesCost: accessoriesCost.toFixed(2),
      laborCost: laborCost.toFixed(2),
      estimatedHours: estimatedHours.toFixed(1),
      designerFee: designerFee.toFixed(2),
      factoryOverhead: ((totalDirectCost * factoryOverhead) / 100).toFixed(2),
      totalDirectCost: totalDirectCost.toFixed(2),
      totalWithOverhead: totalWithOverhead.toFixed(2),
      profitAmount: ((totalWithOverhead * profitMargin) / 100).toFixed(2),
      finalPrice: finalPrice.toFixed(2),
      firstPayment: firstPayment.toFixed(2),
      finalPayment: finalPayment.toFixed(2),
      materialsBreakdown: materials.map((material) => ({
        ...material,
        totalCost: (
          (Number.parseFloat(material.squareMetersNeeded) || 0) * (Number.parseFloat(material.costPerSquareMeter) || 0)
        ).toFixed(2),
      })),
      accessoriesBreakdown: accessories.map((accessory) => ({
        ...accessory,
        totalCost: (
          (Number.parseFloat(accessory.quantity) || 0) * (Number.parseFloat(accessory.costPerUnit) || 0)
        ).toFixed(2),
      })),
    })
  }

  const createProject = () => {
    if (!calculation) {
      alert("يرجى حساب التكاليف أولاً")
      return
    }

    if (!formData.customerId || !formData.assignedEmployee) {
      alert("يرجى اختيار العميل والموظف المكلف")
      return
    }

    // Here you would typically save to database and redirect
    alert("تم إنشاء المشروع بنجاح! سيتم إنشاء الفاتورة الأولية.")
    // Redirect to CRM or projects page
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center space-x-4 space-x-reverse">
          <Link href="/">
            <Button variant="outline" size="sm" className="border-primary-300 text-primary-700 hover:bg-primary-50">
              <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
              العودة للوحة التحكم
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-primary-900">حاسبة تكاليف الإنتاج المتقدمة</h1>
            <p className="text-gray-600">حساب التكاليف بالمتر المربع مع تخصيص الموظفين</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <div className="space-y-6">
            {/* Project Details */}
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="flex items-center space-x-2 space-x-reverse text-primary-900">
                  <Calculator className="h-5 w-5" />
                  <span>تفاصيل المشروع</span>
                </CardTitle>
                <CardDescription className="text-gray-600">المعلومات الأساسية للمشروع</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="projectName" className="text-primary-900">
                      اسم المشروع
                    </Label>
                    <Input
                      id="projectName"
                      value={formData.projectName}
                      onChange={(e) => handleInputChange("projectName", e.target.value)}
                      placeholder="مثال: طاولة طعام مخصصة"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="furnitureType" className="text-primary-900">
                      نوع الأثاث
                    </Label>
                    <Select
                      value={formData.furnitureType}
                      onValueChange={(value) => handleInputChange("furnitureType", value)}
                    >
                      <SelectTrigger className="border-primary-200 focus:border-primary-500">
                        <SelectValue placeholder="اختر النوع" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="table">طاولة</SelectItem>
                        <SelectItem value="chair">كرسي</SelectItem>
                        <SelectItem value="cabinet">خزانة</SelectItem>
                        <SelectItem value="bed">سرير</SelectItem>
                        <SelectItem value="sofa">أريكة</SelectItem>
                        <SelectItem value="other">أخرى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="squareMeters" className="text-primary-900">
                      المساحة بالمتر المربع
                    </Label>
                    <Input
                      id="squareMeters"
                      type="number"
                      step="0.01"
                      value={formData.squareMeters}
                      onChange={(e) => handleInputChange("squareMeters", e.target.value)}
                      placeholder="2.50"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="customerId" className="text-primary-900">
                      العميل
                    </Label>
                    <Select
                      value={formData.customerId}
                      onValueChange={(value) => handleInputChange("customerId", value)}
                    >
                      <SelectTrigger className="border-primary-200 focus:border-primary-500">
                        <SelectValue placeholder="اختر العميل" />
                      </SelectTrigger>
                      <SelectContent>
                        {customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id.toString()}>
                            {customer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="assignedEmployee" className="text-primary-900">
                    الموظف المكلف بالإنتاج
                  </Label>
                  <Select
                    value={formData.assignedEmployee}
                    onValueChange={(value) => handleInputChange("assignedEmployee", value)}
                  >
                    <SelectTrigger className="border-primary-200 focus:border-primary-500">
                      <SelectValue placeholder="اختر الموظف" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Users className="h-4 w-4" />
                            <span>
                              {employee.name} - {employee.specialty}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Materials */}
            <Card className="border-2 border-secondary-200">
              <CardHeader className="bg-secondary-50">
                <CardTitle className="flex items-center justify-between text-secondary-900">
                  <span>المواد الخام (بالمتر المربع)</span>
                  <Button onClick={addMaterial} size="sm" className="bg-secondary-600 hover:bg-secondary-700">
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة مادة
                  </Button>
                </CardTitle>
                <CardDescription className="text-gray-600">أضف جميع المواد المطلوبة للمشروع</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                {materials.map((material, index) => (
                  <div key={material.id} className="p-4 border-2 border-secondary-100 rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-secondary-900">مادة {index + 1}</h4>
                      {materials.length > 1 && (
                        <Button
                          onClick={() => removeMaterial(material.id)}
                          size="sm"
                          variant="outline"
                          className="border-red-300 text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-secondary-900">اسم المادة</Label>
                        <Input
                          value={material.name}
                          onChange={(e) => handleMaterialChange(material.id, "name", e.target.value)}
                          placeholder="خشب البلوط"
                          className="border-secondary-200 focus:border-secondary-500"
                        />
                      </div>
                      <div>
                        <Label className="text-secondary-900">نوع المادة</Label>
                        <Select
                          value={material.type}
                          onValueChange={(value) => handleMaterialChange(material.id, "type", value)}
                        >
                          <SelectTrigger className="border-secondary-200 focus:border-secondary-500">
                            <SelectValue placeholder="اختر النوع" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="oak">خشب البلوط</SelectItem>
                            <SelectItem value="pine">خشب الصنوبر</SelectItem>
                            <SelectItem value="mahogany">خشب الماهوجني</SelectItem>
                            <SelectItem value="plywood">خشب رقائقي</SelectItem>
                            <SelectItem value="mdf">MDF</SelectItem>
                            <SelectItem value="metal">معدن</SelectItem>
                            <SelectItem value="glass">زجاج</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-secondary-900">المساحة المطلوبة (م²)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={material.squareMetersNeeded}
                          onChange={(e) => handleMaterialChange(material.id, "squareMetersNeeded", e.target.value)}
                          placeholder="1.5"
                          className="border-secondary-200 focus:border-secondary-500"
                        />
                      </div>
                      <div>
                        <Label className="text-secondary-900">السعر/م² (د.ل)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={material.costPerSquareMeter}
                          onChange={(e) => handleMaterialChange(material.id, "costPerSquareMeter", e.target.value)}
                          placeholder="12.50"
                          className="border-secondary-200 focus:border-secondary-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Accessories */}
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="flex items-center justify-between text-primary-900">
                  <span>الإكسسوارات والتجهيزات</span>
                  <Button onClick={addAccessory} size="sm" className="bg-primary-600 hover:bg-primary-700">
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة إكسسوار
                  </Button>
                </CardTitle>
                <CardDescription className="text-gray-600">المقابض، المفصلات، والتجهيزات الأخرى</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                {accessories.map((accessory, index) => (
                  <div key={accessory.id} className="p-4 border-2 border-primary-100 rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-primary-900">إكسسوار {index + 1}</h4>
                      {accessories.length > 1 && (
                        <Button
                          onClick={() => removeAccessory(accessory.id)}
                          size="sm"
                          variant="outline"
                          className="border-red-300 text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-primary-900">اسم الإكسسوار</Label>
                        <Input
                          value={accessory.name}
                          onChange={(e) => handleAccessoryChange(accessory.id, "name", e.target.value)}
                          placeholder="مقابض معدنية"
                          className="border-primary-200 focus:border-primary-500"
                        />
                      </div>
                      <div>
                        <Label className="text-primary-900">نوع الإكسسوار</Label>
                        <Select
                          value={accessory.type}
                          onValueChange={(value) => handleAccessoryChange(accessory.id, "type", value)}
                        >
                          <SelectTrigger className="border-primary-200 focus:border-primary-500">
                            <SelectValue placeholder="اختر النوع" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="handles">مقابض</SelectItem>
                            <SelectItem value="hinges">مفصلات</SelectItem>
                            <SelectItem value="locks">أقفال</SelectItem>
                            <SelectItem value="screws">مسامير</SelectItem>
                            <SelectItem value="glue">غراء</SelectItem>
                            <SelectItem value="paint">طلاء</SelectItem>
                            <SelectItem value="varnish">ورنيش</SelectItem>
                            <SelectItem value="other">أخرى</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-primary-900">الكمية</Label>
                        <Input
                          type="number"
                          step="1"
                          value={accessory.quantity}
                          onChange={(e) => handleAccessoryChange(accessory.id, "quantity", e.target.value)}
                          placeholder="4"
                          className="border-primary-200 focus:border-primary-500"
                        />
                      </div>
                      <div>
                        <Label className="text-primary-900">السعر/القطعة (د.ل)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={accessory.costPerUnit}
                          onChange={(e) => handleAccessoryChange(accessory.id, "costPerUnit", e.target.value)}
                          placeholder="3.75"
                          className="border-primary-200 focus:border-primary-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Additional Costs */}
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">التكاليف الإضافية</CardTitle>
                <CardDescription className="text-gray-600">أتعاب التصميم والتكاليف العامة</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="designerFee" className="text-primary-900">
                      أتعاب المصمم (د.ل)
                    </Label>
                    <Input
                      id="designerFee"
                      type="number"
                      step="0.01"
                      value={formData.designerFee}
                      onChange={(e) => handleInputChange("designerFee", e.target.value)}
                      placeholder="50.00"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="factoryOverhead" className="text-primary-900">
                      تكاليف المصنع (%)
                    </Label>
                    <Input
                      id="factoryOverhead"
                      type="number"
                      step="0.1"
                      value={formData.factoryOverhead}
                      onChange={(e) => handleInputChange("factoryOverhead", e.target.value)}
                      placeholder="15"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="profitMargin" className="text-primary-900">
                      هامش الربح (%)
                    </Label>
                    <Input
                      id="profitMargin"
                      type="number"
                      step="0.1"
                      value={formData.profitMargin}
                      onChange={(e) => handleInputChange("profitMargin", e.target.value)}
                      placeholder="25"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                </div>

                <Button onClick={calculateCosts} className="w-full bg-secondary-600 hover:bg-secondary-700">
                  <Calculator className="h-4 w-4 ml-2" />
                  حساب تكلفة الإنتاج
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Results */}
          <Card className="border-2 border-secondary-200">
            <CardHeader className="bg-secondary-50">
              <CardTitle className="text-secondary-900">تفصيل التكاليف المتقدم</CardTitle>
              <CardDescription className="text-gray-600">
                تحليل مفصل لجميع تكاليف الإنتاج بالدينار الليبي
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              {calculation ? (
                <div className="space-y-6">
                  {/* Employee Assignment Display */}
                  {formData.assignedEmployee && (
                    <div className="bg-primary-50 p-4 rounded-lg border-2 border-primary-200">
                      <h4 className="font-semibold text-primary-900 mb-2">الموظف المكلف</h4>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Users className="h-4 w-4 text-primary-600" />
                        <span className="text-primary-700">
                          {employees.find((emp) => emp.id.toString() === formData.assignedEmployee)?.name} -
                          {employees.find((emp) => emp.id.toString() === formData.assignedEmployee)?.specialty}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Materials Breakdown */}
                  {calculation.materialsBreakdown.length > 0 && (
                    <div className="bg-secondary-50 p-4 rounded-lg border-2 border-secondary-200">
                      <h4 className="font-semibold text-secondary-900 mb-3">تفصيل المواد الخام (بالمتر المربع)</h4>
                      <div className="space-y-2">
                        {calculation.materialsBreakdown.map(
                          (material, index) =>
                            material.name && (
                              <div key={index} className="flex justify-between text-sm">
                                <span className="text-secondary-700">
                                  {material.name} ({material.squareMetersNeeded} م²):
                                </span>
                                <span className="font-medium">{material.totalCost} د.ل</span>
                              </div>
                            ),
                        )}
                        <div className="border-t pt-2 flex justify-between font-medium">
                          <span className="text-secondary-900">إجمالي المواد:</span>
                          <span className="text-secondary-900">{calculation.materialsCost} د.ل</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Accessories Breakdown */}
                  {calculation.accessoriesBreakdown.length > 0 && (
                    <div className="bg-primary-50 p-4 rounded-lg border-2 border-primary-200">
                      <h4 className="font-semibold text-primary-900 mb-3">تفصيل الإكسسوارات</h4>
                      <div className="space-y-2">
                        {calculation.accessoriesBreakdown.map(
                          (accessory, index) =>
                            accessory.name && (
                              <div key={index} className="flex justify-between text-sm">
                                <span className="text-primary-700">
                                  {accessory.name} ({accessory.quantity} قطعة):
                                </span>
                                <span className="font-medium">{accessory.totalCost} د.ل</span>
                              </div>
                            ),
                        )}
                        <div className="border-t pt-2 flex justify-between font-medium">
                          <span className="text-primary-900">إجمالي الإكسسوارات:</span>
                          <span className="text-primary-900">{calculation.accessoriesCost} د.ل</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Cost Summary */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">تكلفة المواد:</span>
                        <span className="font-medium">{calculation.materialsCost} د.ل</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">تكلفة الإكسسوارات:</span>
                        <span className="font-medium">{calculation.accessoriesCost} د.ل</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">تكلفة العمالة ({calculation.estimatedHours} ساعة):</span>
                        <span className="font-medium">{calculation.laborCost} د.ل</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">أتعاب المصمم:</span>
                        <span className="font-medium">{calculation.designerFee} د.ل</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">تكاليف المصنع:</span>
                        <span className="font-medium">{calculation.factoryOverhead} د.ل</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">مبلغ الربح:</span>
                        <span className="font-medium">{calculation.profitAmount} د.ل</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">التكلفة المباشرة:</span>
                        <span className="font-medium">{calculation.totalDirectCost} د.ل</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">مع التكاليف الإضافية:</span>
                        <span className="font-medium">{calculation.totalWithOverhead} د.ل</span>
                      </div>
                    </div>
                  </div>

                  <div className="border-t-2 border-secondary-200 pt-4">
                    <div className="flex justify-between items-center text-lg font-bold mb-4">
                      <span className="text-primary-900">السعر النهائي:</span>
                      <span className="text-secondary-600">{calculation.finalPrice} د.ل</span>
                    </div>
                  </div>

                  {/* Payment Breakdown */}
                  <div className="bg-primary-50 p-4 rounded-lg border-2 border-primary-200">
                    <h4 className="font-semibold text-primary-900 mb-3">تفصيل المدفوعات المتدرجة</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-primary-700">الدفعة الأولى (70%):</span>
                        <span className="font-bold text-primary-900">{calculation.firstPayment} د.ل</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-primary-700">الدفعة النهائية (30%):</span>
                        <span className="font-bold text-primary-900">{calculation.finalPayment} د.ل</span>
                      </div>
                    </div>
                    <p className="text-xs text-primary-600 mt-2">
                      * لن يتم تسجيل أي معاملات مالية حتى انتقال الفاتورة من المرحلة الأولية إلى مرحلة التصنيع
                    </p>
                  </div>

                  <div className="flex space-x-2 space-x-reverse pt-4">
                    <Button
                      variant="outline"
                      className="flex-1 border-primary-300 text-primary-700 hover:bg-primary-50"
                    >
                      <Save className="h-4 w-4 ml-2" />
                      حفظ العرض
                    </Button>
                    <Button
                      variant="outline"
                      className="flex-1 border-secondary-300 text-secondary-700 hover:bg-secondary-50"
                    >
                      <FileText className="h-4 w-4 ml-2" />
                      إنشاء PDF
                    </Button>
                    <Button onClick={createProject} className="flex-1 bg-secondary-600 hover:bg-secondary-700">
                      <CreditCard className="h-4 w-4 ml-2" />
                      إنشاء مشروع
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>أدخل تفاصيل المشروع والمواد واضغط حساب لعرض تفصيل التكاليف</p>
                  <p className="text-sm mt-2">تأكد من اختيار العميل والموظف المكلف قبل إنشاء المشروع</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
