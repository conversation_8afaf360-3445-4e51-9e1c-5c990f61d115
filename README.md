# مجموعة H - نظام إدارة مصنع الأثاث (إصدار سطح المكتب)

نظام شامل لإدارة الإنتاج والأعمال مع CRM متقدم وإدارة مالية بالدينار الليبي، مصمم للعمل بدون اتصال بالإنترنت باستخدام قاعدة بيانات SQLite محلية.

## ✨ الميزات الرئيسية

### 🖥️ **تطبيق سطح المكتب**
- **Tauri Framework**: تطبيق سطح مكتب أصلي عالي الأداء
- **عمل بدون إنترنت**: جميع البيانات محفوظة محلياً
- **قاعدة بيانات SQLite**: سريعة وموثوقة
- **واجهة عربية**: مصممة خصيصاً للمستخدم العربي

### 📊 **إدارة شاملة**
- **إدارة المواد**: تتبع المخزون والموردين
- **نظام CRM**: إدارة العملاء والتفاعلات
- **حاسبة التكاليف**: حساب دقيق بالمتر المربع
- **إدارة الرواتب**: نظام رواتب متكامل
- **الخزينة**: إدارة مالية شاملة
- **التقارير**: تقارير مفصلة وقابلة للتصدير

### 💾 **البيانات المحلية**
- **SQLite Database**: قاعدة بيانات محلية سريعة
- **النسخ الاحتياطي**: إنشاء واستعادة النسخ الاحتياطية
- **استيراد/تصدير**: Excel و CSV
- **أمان البيانات**: حماية محلية للبيانات

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية

1. **Node.js** (الإصدار 18 أو أحدث)
2. **Rust** (أحدث إصدار مستقر)
3. **Tauri CLI**

### خطوات التثبيت

1. **تثبيت Rust**:
\`\`\`bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
\`\`\`

2. **تثبيت Tauri CLI**:
\`\`\`bash
cargo install tauri-cli
\`\`\`

3. **استنساخ المشروع**:
\`\`\`bash
git clone https://github.com/hgroup/furniture-factory-desktop
cd furniture-factory-desktop
\`\`\`

4. **تثبيت التبعيات**:
\`\`\`bash
npm install
\`\`\`

5. **تشغيل التطبيق في وضع التطوير**:
\`\`\`bash
npm run tauri:dev
\`\`\`

6. **بناء التطبيق للإنتاج**:
\`\`\`bash
npm run tauri:build
\`\`\`

## 📁 هيكل المشروع

\`\`\`
furniture-factory-desktop/
├── src-tauri/                 # كود Rust للتطبيق
│   ├── src/
│   │   ├── main.rs           # نقطة دخول التطبيق
│   │   ├── database/         # إدارة قاعدة البيانات
│   │   ├── models/           # نماذج البيانات
│   │   ├── commands/         # أوامر Tauri
│   │   └── error.rs          # معالجة الأخطاء
│   ├── migrations/           # ملفات ترحيل قاعدة البيانات
│   ├── Cargo.toml           # تبعيات Rust
│   └── tauri.conf.json      # إعدادات Tauri
├── app/                      # تطبيق Next.js
├── components/               # مكونات React
├── lib/                      # مكتبات مساعدة
├── hooks/                    # React Hooks مخصصة
└── public/                   # الملفات العامة
\`\`\`

## 🗄️ قاعدة البيانات

التطبيق يستخدم SQLite كقاعدة بيانات محلية مع الجداول التالية:

- **materials**: إدارة المواد الخام
- **customers**: بيانات العملاء
- **employees**: معلومات الموظفين
- **projects**: المشاريع والطلبات
- **payments**: المدفوعات والفواتير
- **transactions**: المعاملات المالية
- **activities**: سجل الأنشطة
- **accounts**: الحسابات المالية
- **backups**: النسخ الاحتياطية

## 🔧 الإعدادات

### إعدادات قاعدة البيانات

قاعدة البيانات تُحفظ في:
- **Windows**: `%APPDATA%/furniture_factory/furniture_factory.db`
- **macOS**: `~/Library/Application Support/furniture_factory/furniture_factory.db`
- **Linux**: `~/.local/share/furniture_factory/furniture_factory.db`

### النسخ الاحتياطية

يمكن إنشاء نسخ احتياطية من خلال:
1. قائمة الملف → إنشاء نسخة احتياطية
2. حفظ تلقائي يومي
3. تصدير البيانات إلى Excel/CSV

## 📱 واجهة المستخدم

### الميزات الرئيسية:
- **تصميم عربي**: واجهة مصممة للغة العربية
- **وضع محلي**: مؤشر يظهر أن التطبيق يعمل محلياً
- **إشعارات**: تنبيهات للعمليات المهمة
- **اختصارات لوحة المفاتيح**: للوصول السريع
- **نظام الصواني**: تشغيل في الخلفية

### الصفحات الرئيسية:
1. **لوحة التحكم**: نظرة عامة على النشاط
2. **إدارة المواد**: تتبع المخزون
3. **إدارة العملاء**: نظام CRM
4. **حاسبة الإنتاج**: حساب التكاليف
5. **إدارة الرواتب**: نظام الموظفين
6. **الخزينة**: الإدارة المالية
7. **التقارير**: تحليلات مفصلة

## 🔒 الأمان

- **تشفير البيانات**: حماية قاعدة البيانات
- **نسخ احتياطية آمنة**: تشفير النسخ الاحتياطية
- **صلاحيات محدودة**: وصول محدود للملفات
- **تسجيل الأنشطة**: تتبع جميع العمليات

## 🛠️ التطوير

### إضافة ميزات جديدة:

1. **إضافة نموذج جديد** في `src-tauri/src/models/`
2. **إنشاء أوامر Tauri** في `src-tauri/src/commands/`
3. **إضافة صفحات React** في `app/`
4. **تحديث قاعدة البيانات** في `migrations/`

### اختبار التطبيق:

\`\`\`bash
# تشغيل في وضع التطوير
npm run tauri:dev

# بناء للاختبار
npm run tauri:build

# اختبار قاعدة البيانات
cargo test --manifest-path=src-tauri/Cargo.toml
\`\`\`

## 📦 التوزيع

### بناء للإنتاج:

\`\`\`bash
npm run tauri:build
\`\`\`

سيتم إنشاء ملفات التثبيت في:
- **Windows**: `.msi` و `.exe`
- **macOS**: `.dmg` و `.app`
- **Linux**: `.deb` و `.AppImage`

### التوقيع الرقمي:

لتوقيع التطبيق، أضف شهادة التوقيع في `tauri.conf.json`:

\`\`\`json
{
  "bundle": {
    "windows": {
      "certificateThumbprint": "YOUR_CERTIFICATE_THUMBPRINT",
      "digestAlgorithm": "sha256",
      "timestampUrl": "http://timestamp.sectigo.com"
    }
  }
}
\`\`\`

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في قاعدة البيانات**:
   - تحقق من صلاحيات الكتابة
   - احذف قاعدة البيانات وأعد إنشاءها

2. **مشاكل في البناء**:
   - تأكد من تثبيت Rust بشكل صحيح
   - نظف cache: `cargo clean`

3. **مشاكل في الواجهة**:
   - تحقق من console للأخطاء
   - أعد تشغيل التطبيق

### سجلات الأخطاء:

السجلات محفوظة في:
- **Windows**: `%APPDATA%/furniture_factory/logs/`
- **macOS**: `~/Library/Logs/furniture_factory/`
- **Linux**: `~/.local/share/furniture_factory/logs/`

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [إنشاء issue جديد](https://github.com/hgroup/furniture-factory-desktop/issues)
- **الوثائق**: [دليل المستخدم](https://docs.hgroup.ly)

---

**مجموعة H** - نظام إدارة مصنع الأثاث
© 2024 جميع الحقوق محفوظة
