use crate::database::Database;
use crate::error::Result;
use tauri::State;

#[tauri::command]
pub async fn generate_production_report(db: State<'_, Database>, start_date: String, end_date: String) -> Result<String> {
    db.generate_production_report(start_date, end_date).await
}

#[tauri::command]
pub async fn generate_financial_report(db: State<'_, Database>, start_date: String, end_date: String) -> Result<String> {
    db.generate_financial_report(start_date, end_date).await
}

#[tauri::command]
pub async fn export_report(db: State<'_, Database>, report_type: String, format: String) -> Result<String> {
    db.export_report(report_type, format).await
}
