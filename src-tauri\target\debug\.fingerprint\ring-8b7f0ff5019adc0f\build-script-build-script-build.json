{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 13307531992580020898, "deps": [[1449426130110690549, "cc", false, 11291408524358591476]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-8b7f0ff5019adc0f\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}